# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Wenla (文拉) is a professional live streaming practice app for e-commerce hosts. It integrates AI analysis, speech recognition, and real-time interaction features to help streamers improve their live selling skills.

## Development Environment & Commands

### Build and Run
- **Open in Xcode**: Use `wenla.xcodeproj` to open the project
- **Build**: `Command + B` in Xcode or `xcodebuild` from command line
- **Run**: `Command + R` in Xcode or use iOS Simulator
- **Test**: `Command + U` in Xcode or `xcodebuild test`

### Target Requirements
- **iOS Version**: 18.5 or higher
- **Device Support**: iPhone with front-facing camera
- **Permissions**: Camera, microphone, speech recognition
- **Network**: Required for AI analysis features

### Dependencies
- **AVFoundation**: Camera and audio capture
- **Speech**: Speech recognition (Chinese language support)
- **SwiftUI**: Modern UI framework
- **Foundation**: Core functionality

## Architecture Overview

### Core Components

#### 1. Main App Structure
- **wenlaApp.swift**: App entry point with idle timer management
- **ContentView.swift**: Root view handling welcome/live stream navigation
- **WelcomeView.swift**: Initial screen with user settings
- **LiveStreamView.swift**: Main live streaming interface

#### 2. Manager Classes (Business Logic)
- **LiveStreamManager**: Controls live stream state, statistics, and chat simulation
- **CameraManager**: Handles camera permissions, capture session, and camera switching
- **ECommerceManager**: Manages product analysis, AI question generation, and live stream summaries
- **SpeechRecognitionManager**: Handles speech-to-text conversion for Chinese language
- **AIModelManager**: Manages multiple AI providers (DeepSeek, Gemini) with fallback support

#### 3. UI Components
- **SettingsView.swift**: User profile configuration
- **ProductInputView.swift**: Product URL input and analysis interface
- **LiveStreamSummaryView.swift**: Post-stream performance analysis
- **QuestionConfirmationView.swift**: AI-generated question approval
- **CameraPreviewView.swift**: Camera preview display

#### 4. Data Models
- **Models.swift**: Core data structures including:
  - `LiveStats`: Live stream statistics
  - `UserSettings`: User profile and preferences
  - `ChatMessage`: Chat message structure
  - `AIModel`: AI model configuration
  - `ProductInfo`: E-commerce product data
  - `CustomerQuestion`: Generated customer questions

### Key Features

#### AI Integration
- **Multiple AI Providers**: DeepSeek R1 and Gemini models
- **Fallback System**: Automatic failover between AI models
- **Question Generation**: AI analyzes product URLs to generate realistic customer questions
- **Performance Analysis**: AI-powered live stream performance evaluation

#### Live Stream Simulation
- **Camera Integration**: Front/back camera switching with permission handling
- **Real-time Chat**: Simulated viewer messages and interactions
- **Statistics Display**: Live viewer count, likes, and engagement metrics
- **Question Injection**: AI-generated questions appear as chat messages

#### Speech Recognition
- **Chinese Language Support**: Optimized for Mandarin Chinese
- **Real-time Transcription**: Live speech-to-text conversion
- **Session Management**: Automatic recording start/stop with stream

### API Configuration

#### DeepSeek API
- **Base URL**: `https://api.deepseek.com/v1`
- **Models**: `deepseek-reasoner`, `deepseek-chat`
- **Configuration**: Located in `AIModelManager.swift:19`

#### Gemini API  
- **Base URL**: `https://generativelanguage.googleapis.com/v1beta`
- **Models**: `gemini-1.5-pro`, `gemini-1.5-flash` (default)
- **Configuration**: Located in `AIModelManager.swift:20`

### Data Flow

1. **User Journey**: WelcomeView → LiveStreamView → ProductInputView → AI Analysis → Question Generation → Live Practice → Summary
2. **AI Pipeline**: Product URL → AI Analysis → Question Generation → Chat Integration
3. **Speech Pipeline**: Microphone → Speech Recognition → Transcript → Performance Analysis

### Error Handling

- **AI Fallback**: Automatic switching between AI models on failure
- **Permission Management**: Graceful handling of camera/microphone permissions
- **Network Resilience**: Local fallback responses when AI services are unavailable
- **User Feedback**: Clear error messages and retry mechanisms

### Development Notes

- **Simulator Support**: Camera functionality has simulator-specific implementations
- **Performance**: Optimized token usage and timeout settings for faster AI responses
- **Memory Management**: Proper cleanup of camera sessions and speech recognition resources
- **Chinese Localization**: UI and content optimized for Chinese users

### Testing

- **Unit Tests**: `wenlaTests/wenlaTests.swift`
- **UI Tests**: `wenlaUITests/wenlaUITests.swift` and `wenlaUITestsLaunchTests.swift`
- **Device Testing**: Requires physical iOS device for full camera/microphone functionality

### Key Files to Modify

- **AI Configuration**: `AIModelManager.swift` for API keys and model settings
- **Product Analysis**: `ECommerceManager.swift` for question generation logic
- **UI Customization**: Individual view files for interface changes
- **App Settings**: `wenlaApp.swift` for app-level configuration