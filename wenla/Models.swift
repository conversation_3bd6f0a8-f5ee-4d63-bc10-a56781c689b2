//
//  Models.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import Foundation
import SwiftUI

// MARK: - 直播统计数据
class LiveStats: ObservableObject {
    @Published var viewerCount: Int = 0
    @Published var likeCount: Int = 0
    @Published var followCount: Int = 0
    @Published var giftCount: Int = 0
    @Published var duration: TimeInterval = 0

    // 新增：格式化的点赞数显示
    var formattedLikeCount: String {
        if likeCount >= 10000 {
            return String(format: "%.1f万", Double(likeCount) / 10000.0)
        } else if likeCount >= 1000 {
            return String(format: "%.1fk", Double(likeCount) / 1000.0)
        } else {
            return "\(likeCount)"
        }
    }

    // 新增：格式化的观看人数显示
    var formattedViewerCount: String {
        if viewerCount >= 100000 {
            // 超过10万显示10万+
            return "10万+"
        } else if viewerCount >= 10000 {
            let value = Double(viewerCount) / 10000.0
            // 去掉不必要的小数点，如果是整数就不显示小数
            if value.truncatingRemainder(dividingBy: 1) == 0 {
                return String(format: "%.0f万", value)
            } else {
                return String(format: "%.1f万", value)
            }
        } else {
            // 10000以下直接显示数字，不使用千位分隔符
            return "\(viewerCount)"
        }
    }
}

// MARK: - AI模型配置
enum AIModel: String, CaseIterable {
    case doubaoSeed = "doubao-seed-1-6-250615"

    var displayName: String {
        switch self {
        case .doubaoSeed:
            return "豆包 Seed 1.6"
        }
    }

    var provider: AIProvider {
        switch self {
        case .doubaoSeed:
            return .doubao
        }
    }

    var isDefault: Bool {
        return self == .doubaoSeed // 豆包为唯一模型
    }
}

enum AIProvider: String {
    case doubao = "doubao"

    var baseURL: String {
        switch self {
        case .doubao:
            // 根据官方文档使用正确的API端点
            return "https://ark.cn-beijing.volces.com/api/v3"
        }
    }
}

// MARK: - AI模型配置
struct AIModelConfig {
    let model: AIModel
    let apiKey: String
    let maxTokens: Int
    let temperature: Double?

    static let doubaoConfig = AIModelConfig(
        model: .doubaoSeed,
        apiKey: "e9e21053-6373-4315-b22f-196e5ab9fb04",
        maxTokens: 1500,  // 优化token数量
        temperature: 0.6  // 优化温度参数
    )
}

// MARK: - 用户设置
class UserSettings: ObservableObject {
    @Published var username: String = "主播" {
        didSet {
            saveSettings()
        }
    }
    @Published var userAvatar: String? = nil { // 用户头像emoji，nil表示使用自定义图片
        didSet {
            saveSettings()
        }
    }
    @Published var userAvatarImageData: Data? = nil { // 用户自定义头像图片数据
        didSet {
            saveSettings()
        }
    }
    @Published var defaultAvatarEmoji: String = "👩‍🎤" // 默认头像emoji

    // UserDefaults keys
    private let usernameKey = "UserSettings_Username"
    private let userAvatarKey = "UserSettings_UserAvatar"
    private let userAvatarImageDataKey = "UserSettings_UserAvatarImageData"

    init() {
        loadSettings()
    }

    // 获取显示用的头像emoji（仅用于emoji头像）
    var displayAvatar: String {
        return userAvatar ?? defaultAvatarEmoji
    }

    // 是否使用默认头像
    var isUsingDefaultAvatar: Bool {
        return userAvatar == nil && userAvatarImageData == nil
    }

    // 是否使用emoji头像
    var isUsingEmojiAvatar: Bool {
        return userAvatar != nil
    }

    // 是否使用自定义图片头像
    var isUsingCustomImageAvatar: Bool {
        return userAvatarImageData != nil
    }

    // 设置emoji头像
    func setEmojiAvatar(_ emoji: String) {
        userAvatar = emoji
        userAvatarImageData = nil // 清除图片数据
    }

    // 设置自定义图片头像
    func setCustomImageAvatar(_ imageData: Data) {
        // 验证图片数据的有效性
        guard imageData.count > 0,
              UIImage(data: imageData) != nil else {
            print("❌ 无效的图片数据，无法设置头像")
            return
        }

        // 检查图片大小，如果太大则拒绝
        let maxSizeKB = 200 // 最大200KB
        if imageData.count > maxSizeKB * 1024 {
            print("❌ 图片太大 (\(imageData.count / 1024)KB)，请选择更小的图片")
            return
        }

        userAvatarImageData = imageData
        userAvatar = nil // 清除emoji
        print("✅ 头像设置成功，大小: \(imageData.count / 1024)KB")
    }

    // 重置为默认头像
    func resetToDefaultAvatar() {
        userAvatar = nil
        userAvatarImageData = nil
    }

    // 安全地获取头像UIImage
    func getSafeAvatarImage() -> UIImage? {
        guard let imageData = userAvatarImageData else { return nil }

        // 尝试创建UIImage，如果失败则清除损坏的数据
        if let image = UIImage(data: imageData) {
            return image
        } else {
            print("⚠️ 检测到损坏的头像数据，正在清除...")
            DispatchQueue.main.async { [weak self] in
                self?.userAvatarImageData = nil
            }
            return nil
        }
    }

    // MARK: - 数据持久化
    private func saveSettings() {
        UserDefaults.standard.set(username, forKey: usernameKey)
        UserDefaults.standard.set(userAvatar, forKey: userAvatarKey)
        UserDefaults.standard.set(userAvatarImageData, forKey: userAvatarImageDataKey)
    }

    private func loadSettings() {
        username = UserDefaults.standard.string(forKey: usernameKey) ?? "主播"
        userAvatar = UserDefaults.standard.string(forKey: userAvatarKey)

        // 加载头像数据时进行验证
        if let imageData = UserDefaults.standard.data(forKey: userAvatarImageDataKey) {
            // 验证数据是否有效
            if UIImage(data: imageData) != nil {
                userAvatarImageData = imageData
                print("✅ 头像数据加载成功，大小: \(imageData.count / 1024)KB")
            } else {
                print("⚠️ 检测到损坏的头像数据，已清除")
                UserDefaults.standard.removeObject(forKey: userAvatarImageDataKey)
                userAvatarImageData = nil
            }
        }
    }
}



// MARK: - 直播设置
class LiveSettings: ObservableObject {
    @Published var minViewers: Int = 100
    @Published var maxViewers: Int = 500
    @Published var chatFrequency: Double = 1.0  // 每秒弹幕数量
    @Published var likeFrequency: Double = 3.0  // 每秒点赞数量
    @Published var giftFrequency: Double = 0.1  // 每秒礼物数量
}

// MARK: - 聊天消息
struct ChatMessage: Identifiable {
    let id = UUID()
    let username: String
    let message: String
    let timestamp: Date
    let messageType: MessageType
    let userLevel: Int
    let avatarIcon: String // 随机头像图标

    init(username: String, message: String, timestamp: Date, messageType: MessageType, userLevel: Int) {
        self.username = username
        self.message = message
        self.timestamp = timestamp
        self.messageType = messageType
        self.userLevel = userLevel
        self.avatarIcon = ChatMessage.randomAvatarIcon()
    }

    // 随机头像图标数组
    private static let avatarIcons = [
        "person.circle.fill", "person.crop.circle.fill", "person.2.circle.fill",
        "face.smiling.fill", "face.dashed.fill", "eyes.inverse",
        "heart.circle.fill", "star.circle.fill", "crown.fill",
        "gamecontroller.fill", "headphones.circle.fill", "music.note.circle.fill",
        "camera.circle.fill", "video.circle.fill", "phone.circle.fill",
        "message.circle.fill", "envelope.circle.fill", "paperplane.circle.fill",
        "gift.circle.fill", "balloon.2.fill", "party.popper.fill",
        "sparkles", "flame.fill", "bolt.circle.fill"
    ]

    private static func randomAvatarIcon() -> String {
        return avatarIcons.randomElement() ?? "person.circle.fill"
    }
}

enum MessageType {
    case normal
    case gift
    case welcome
    case follow
}

// MARK: - 礼物数据
struct Gift: Identifiable {
    let id = UUID()
    let name: String
    let emoji: String
    let value: Int
    let color: Color
}

// MARK: - 聊天内容数据
struct ChatContent {
    static let usernames = [
        "夜雨听风", "星辰大海", "清风徐来", "墨染青衣", "浅笑安然",
        "北城以北", "南风过境", "时光荏苒", "岁月如歌", "梦里花落",
        "青春无悔", "往事如烟", "初心不改", "温柔半两", "孤独患者",
        "深海少女", "森林散布", "城南花开", "北巷不夏", "南街浊酒",
        "西窗烛", "东篱菊", "山河故人", "江南烟雨", "塞北雪花",
        "海棠花开", "梧桐叶落", "竹林听雨", "荷塘月色", "桃花依旧",
        "樱花飞舞", "枫叶正红", "柳絮飞扬", "梅花三弄", "兰花幽香",
        "菊花残", "荷花香", "桂花飘", "茉莉清", "玫瑰红",
        "向日葵", "薰衣草", "满天星", "勿忘我", "彼岸花"
    ]

    static let normalMessages = [
        "666", "主播好棒！", "太厉害了", "支持支持", "加油！",
        "好看！", "喜欢你", "继续继续", "太有趣了", "笑死我了",
        "主播真可爱", "声音好听", "颜值很高", "才华横溢", "期待更多",
        "太精彩了", "学到了", "感谢分享", "很有意思", "继续关注",
        "主播辛苦了", "内容很棒", "很有创意", "支持原创", "点赞点赞"
    ]

    // 来了类型的弹幕
    static let arrivedMessages = [
        "来了来了！", "刚到！", "准时来了", "终于等到了", "来支持主播了",
        "下班就来了", "来看看", "来学习了", "来了兄弟们", "准时报到",
        "来晚了吗？", "刚进来", "来围观", "来了来了", "准时守候",
        "来看直播了", "来了老铁", "刚上线", "来支持了", "准时到达",
        "来学习带货", "来看产品", "来了解一下", "刚进直播间", "来看看新品"
    ]

    // 产品相关的长弹幕（用于模拟AI生成的问题）
    static let productQuestions = [
        "主播这个产品真的很不错，我之前买过类似的，质量确实很好，推荐大家购买！",
        "请问这个商品有什么优惠活动吗？我想买几个送给朋友，希望能有个好价格。",
        "主播能详细介绍一下这个产品的使用方法吗？我是第一次接触这类商品，不太了解。",
        "这个颜色真的太好看了！请问还有其他颜色可以选择吗？我想要粉色的。",
        "主播的讲解真的很专业，让我对这个产品有了更深入的了解，谢谢分享！",
        "这个产品的质量怎么样？会不会容易坏？",
        "请问有售后保障吗？如果不满意可以退货吗？",
        "这个价格在同类产品中算便宜还是贵？",
        "适合什么年龄段的人使用？",
        "使用起来会不会很复杂？",
        "这个品牌可靠吗？有什么认证？",
        "发货速度快吗？什么时候能收到？",
        "包装怎么样？会不会在运输中损坏？",
        "有没有使用教程或者说明书？",
        "这个产品有什么特别的功能吗？",
        "与其他品牌相比有什么优势？",
        "使用寿命大概多长？",
        "需要定期维护吗？",
        "有没有配件需要单独购买？",
        "适合送人吗？包装好看吗？"
    ]
}

// MARK: - 快捷商品数据模型
struct QuickProduct {
    let id = UUID()
    let name: String
    let description: String
    let icon: String
    let iconColor: Color
    let productInfo: ProductInfo
}

// MARK: - 礼物数据
struct GiftData {
    static let gifts = [
        Gift(name: "玫瑰", emoji: "🌹", value: 1, color: .red),
        Gift(name: "啤酒", emoji: "🍺", value: 2, color: .orange),
        Gift(name: "蛋糕", emoji: "🎂", value: 8, color: .pink),
        Gift(name: "钻石", emoji: "💎", value: 20, color: .blue),
        Gift(name: "跑车", emoji: "🚗", value: 99, color: .green),
        Gift(name: "火箭", emoji: "🚀", value: 500, color: .purple),
        Gift(name: "城堡", emoji: "🏰", value: 1000, color: .yellow)
    ]
}

// MARK: - 氛围表情包动画
struct AtmosphereEmoji {
    let id: UUID
    var x: CGFloat
    var y: CGFloat
    var opacity: Double
    var scale: CGFloat
    var rotation: Double
    let emoji: String
    let animationType: AtmosphereAnimationType

    init(emoji: String, x: CGFloat, y: CGFloat, animationType: AtmosphereAnimationType = .float) {
        self.id = UUID()
        self.emoji = emoji
        self.x = x
        self.y = y
        self.opacity = 1.0
        self.scale = CGFloat.random(in: 0.8...1.2)
        self.rotation = 0
        self.animationType = animationType
    }
}

enum AtmosphereAnimationType {
    case float      // 漂浮上升
    case bounce     // 弹跳
    case spin       // 旋转
    case wave       // 波浪
}

// MARK: - 氛围表情包数据
struct AtmosphereEmojiData {
    // 热烈氛围表情包
    static let celebrationEmojis = [
        "🎉", "🎊", "✨", "🌟", "⭐", "💫", "🎆", "🎇",
        "🔥", "💥", "⚡", "💯", "🚀", "🎯", "🏆", "🥇",
        "👏", "🙌", "👍", "💪", "✊", "👊", "🤝", "🙏",
        "❤️", "💖", "💕", "💗", "💓", "💝", "💘", "💞",
        "😍", "🤩", "😘", "😊", "😄", "😆", "🥳", "🤗",
        "🎵", "🎶", "🎼", "🎤", "🎸", "🥳", "🎭", "🎨"
    ]

    // 购物相关表情包
    static let shoppingEmojis = [
        "💰", "💎", "💳", "🛍️", "🛒", "🎁", "🏷️", "💸",
        "🤑", "💴", "💵", "💶", "💷", "🪙", "💲", "📦",
        "🎀", "🎊", "🎉", "✨", "🌟", "⭐", "💫", "🔥"
    ]

    // 支持鼓励表情包
    static let supportEmojis = [
        "👏", "🙌", "👍", "💪", "✊", "👊", "🤝", "🙏",
        "❤️", "💖", "💕", "💗", "💓", "💝", "💘", "💞",
        "😍", "🤩", "😘", "😊", "😄", "😆", "🥰", "😇"
    ]

    // 获取随机表情包
    static func getRandomEmoji() -> String {
        let allEmojis = celebrationEmojis + shoppingEmojis + supportEmojis
        return allEmojis.randomElement() ?? "🎉"
    }

    // 根据类型获取表情包
    static func getRandomEmoji(type: EmojiType) -> String {
        switch type {
        case .celebration:
            return celebrationEmojis.randomElement() ?? "🎉"
        case .shopping:
            return shoppingEmojis.randomElement() ?? "💰"
        case .support:
            return supportEmojis.randomElement() ?? "👏"
        }
    }
}

enum EmojiType {
    case celebration    // 庆祝类
    case shopping      // 购物类
    case support       // 支持类
}
