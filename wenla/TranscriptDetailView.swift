//
//  TranscriptDetailView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/11.
//

import SwiftUI

struct TranscriptDetailView: View {
    let transcript: [TranscriptSegment]
    let duration: TimeInterval
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedSpeaker: String = "全部"
    
    private let speakers = ["全部", "主播", "观众"]
    
    var filteredTranscript: [TranscriptSegment] {
        transcript.filter { segment in
            let matchesSpeaker = selectedSpeaker == "全部" || segment.speaker == selectedSpeaker
            let matchesSearch = searchText.isEmpty || segment.content.localizedCaseInsensitiveContains(searchText)
            return matchesSpeaker && matchesSearch
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索和筛选区域
                VStack(spacing: 12) {
                    // 搜索框
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                        
                        TextField("搜索转录内容...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    
                    // 说话人筛选
                    HStack {
                        Text("说话人:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Picker("说话人", selection: $selectedSpeaker) {
                            ForEach(speakers, id: \.self) { speaker in
                                Text(speaker).tag(speaker)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }
                }
                .padding()
                .background(Color(.systemGroupedBackground))
                
                // 统计信息
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("总计 \(filteredTranscript.count) 条记录")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if !searchText.isEmpty {
                            Text("搜索结果")
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Spacer()
                    
                    // 平均置信度
                    if !filteredTranscript.isEmpty {
                        let avgConfidence = filteredTranscript.map { $0.confidence }.reduce(0, +) / Double(filteredTranscript.count)
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("平均置信度")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Text("\(Int(avgConfidence * 100))%")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(confidenceColor(avgConfidence))
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 8)
                
                // 转录内容列表
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(filteredTranscript, id: \.id) { segment in
                            TranscriptRow(segment: segment, searchText: searchText)
                        }
                    }
                    .padding()
                }
                .background(Color(.systemGroupedBackground))
            }
            .navigationTitle("直播转录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: exportTranscript) {
                            Label("导出文本", systemImage: "square.and.arrow.up")
                        }
                        
                        Button(action: shareTranscript) {
                            Label("分享", systemImage: "square.and.arrow.up")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let minutes = Int(timestamp) / 60
        let seconds = Int(timestamp) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func confidenceColor(_ confidence: Double) -> Color {
        switch confidence {
        case 0.9...1.0:
            return .green
        case 0.8..<0.9:
            return .orange
        default:
            return .red
        }
    }
    
    private func exportTranscript() {
        // 导出转录文本的逻辑
        print("导出转录文本")
    }
    
    private func shareTranscript() {
        // 分享转录文本的逻辑
        print("分享转录文本")
    }
}

// MARK: - 转录行视图
struct TranscriptRow: View {
    let segment: TranscriptSegment
    let searchText: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 时间戳
            VStack(spacing: 4) {
                Text(formatTimestamp(segment.timestamp))
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                
                // 置信度指示器
                Circle()
                    .fill(confidenceColor(segment.confidence))
                    .frame(width: 6, height: 6)
            }
            .frame(width: 50)
            
            // 说话人标识
            Text(segment.speaker)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(segment.speaker == "主播" ? Color.blue : Color.green)
                .cornerRadius(12)
            
            // 转录内容
            VStack(alignment: .leading, spacing: 4) {
                if searchText.isEmpty {
                    Text(segment.content)
                        .font(.body)
                        .foregroundColor(.primary)
                } else {
                    // 高亮搜索文本
                    Text(highlightedText(segment.content, searchText: searchText))
                        .font(.body)
                }
                
                // 置信度
                HStack {
                    Text("置信度: \(Int(segment.confidence * 100))%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let minutes = Int(timestamp) / 60
        let seconds = Int(timestamp) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func confidenceColor(_ confidence: Double) -> Color {
        switch confidence {
        case 0.9...1.0:
            return .green
        case 0.8..<0.9:
            return .orange
        default:
            return .red
        }
    }
    
    private func highlightedText(_ text: String, searchText: String) -> AttributedString {
        var attributedString = AttributedString(text)
        
        if let range = attributedString.range(of: searchText, options: .caseInsensitive) {
            attributedString[range].backgroundColor = .yellow
            attributedString[range].foregroundColor = .black
        }
        
        return attributedString
    }
}

#Preview {
    TranscriptDetailView(
        transcript: [
            TranscriptSegment(
                timestamp: 120,
                speaker: "主播",
                content: "大家好，欢迎来到我的直播间！今天给大家带来一款非常棒的智能手机",
                confidence: 0.95
            ),
            TranscriptSegment(
                timestamp: 135,
                speaker: "观众",
                content: "这款手机多少钱？",
                confidence: 0.88
            ),
            TranscriptSegment(
                timestamp: 150,
                speaker: "主播",
                content: "这款手机的价格是3999元，现在有优惠活动",
                confidence: 0.92
            )
        ],
        duration: 1800
    )
}
