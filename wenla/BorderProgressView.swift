//
//  BorderProgressView.swift
//  wenla
//
//  Created by AI Assistant on 2025-07-12.
//

import SwiftUI

struct BorderProgressView: View {
    let isActive: Bool
    @State private var animationProgress: Double = 0.0
    @State private var animationTimer: Timer?

    init(isActive: Bool = true) {
        self.isActive = isActive
    }

    var body: some View {
        if isActive {
            ZStack {
                // 静态彩色边框（降低透明度作为背景）
                RoundedRectangle(cornerRadius: 40)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                .blue, .purple, .pink, .orange,
                                .yellow, .green, .cyan, .blue
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 6
                    )
                    .opacity(0.3)

                // 移动的散射光标效果
                MovingLightCursor(progress: animationProgress)
                    .onAppear {
                        startAnimation()
                    }
                    .onDisappear {
                        stopAnimation()
                    }

                // 内层细边框
                RoundedRectangle(cornerRadius: 40)
                    .stroke(
                        Color.white.opacity(0.4),
                        lineWidth: 2
                    )
                    .shadow(color: .cyan.opacity(0.3), radius: 6, x: 0, y: 0)
            }
            .padding(8)
            .ignoresSafeArea()
        }
    }

    private func startAnimation() {
        // 使用 Timer 替代无限动画，更安全
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { _ in
            withAnimation(.linear(duration: 0.05)) {
                animationProgress += 0.01
                if animationProgress >= 1.0 {
                    animationProgress = 0.0
                }
            }
        }
    }

    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
}

// 优雅的跑马灯效果组件
struct MovingLightCursor: View {
    let progress: Double

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 计算路径上的当前位置
                let pathPoints = calculatePathPoints(in: geometry.size)

                // 创建流动的光效 - 简洁而优雅
                createFlowingLight(pathPoints: pathPoints)
            }
        }
    }

    // 创建流动光效
    private func createFlowingLight(pathPoints: [CGPoint]) -> some View {
        ZStack {
            // 主光流 - 一条连续的光带
            createLightTrail(
                pathPoints: pathPoints,
                startProgress: progress,
                length: 0.25, // 光带长度占路径的25%
                color: .cyan,
                width: 4,
                intensity: 0.9
            )

            // 次光流 - 更细的光带，稍微滞后
            createLightTrail(
                pathPoints: pathPoints,
                startProgress: progress - 0.1,
                length: 0.15,
                color: .white,
                width: 2,
                intensity: 0.7
            )

            // 前导光点 - 在主光流前方
            createLeadingDot(
                pathPoints: pathPoints,
                progress: progress + 0.05
            )
        }
    }

    // 创建光带
    private func createLightTrail(pathPoints: [CGPoint], startProgress: Double, length: Double, color: Color, width: CGFloat, intensity: Double) -> some View {
        let normalizedStart = startProgress.truncatingRemainder(dividingBy: 1.0)
        let normalizedEnd = (startProgress + length).truncatingRemainder(dividingBy: 1.0)

        return Path { path in
            let startIndex = Int(normalizedStart * Double(pathPoints.count - 1))
            let endIndex = Int(normalizedEnd * Double(pathPoints.count - 1))

            if normalizedStart < normalizedEnd {
                // 不跨界的情况
                for i in startIndex...endIndex {
                    let point = pathPoints[min(i, pathPoints.count - 1)]
                    if i == startIndex {
                        path.move(to: point)
                    } else {
                        path.addLine(to: point)
                    }
                }
            } else {
                // 跨界的情况
                for i in startIndex..<pathPoints.count {
                    let point = pathPoints[i]
                    if i == startIndex {
                        path.move(to: point)
                    } else {
                        path.addLine(to: point)
                    }
                }
                for i in 0...endIndex {
                    path.addLine(to: pathPoints[i])
                }
            }
        }
        .stroke(
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: color.opacity(0.1), location: 0),
                    .init(color: color.opacity(intensity), location: 0.5),
                    .init(color: color.opacity(0.1), location: 1)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            ),
            style: StrokeStyle(lineWidth: width, lineCap: .round, lineJoin: .round)
        )
        .shadow(color: color.opacity(0.6), radius: width, x: 0, y: 0)
    }

    // 创建前导光点
    private func createLeadingDot(pathPoints: [CGPoint], progress: Double) -> some View {
        let normalizedProgress = progress.truncatingRemainder(dividingBy: 1.0)
        let currentIndex = Int(normalizedProgress * Double(pathPoints.count - 1))
        let currentPoint = pathPoints[min(currentIndex, pathPoints.count - 1)]

        return Circle()
            .fill(
                RadialGradient(
                    gradient: Gradient(colors: [
                        .white,
                        .cyan.opacity(0.8),
                        .clear
                    ]),
                    center: .center,
                    startRadius: 1,
                    endRadius: 6
                )
            )
            .frame(width: 12, height: 12)
            .position(currentPoint)
            .shadow(color: .cyan, radius: 6, x: 0, y: 0)
    }

    // 计算圆角矩形路径上的点
    private func calculatePathPoints(in size: CGSize) -> [CGPoint] {
        var points: [CGPoint] = []
        let cornerRadius: CGFloat = 40
        let inset: CGFloat = 8
        let rect = CGRect(x: inset, y: inset, width: size.width - inset * 2, height: size.height - inset * 2)

        let steps = 200 // 路径上的点数

        for i in 0..<steps {
            let progress = Double(i) / Double(steps - 1)
            let point = pointOnRoundedRectPath(rect: rect, cornerRadius: cornerRadius, progress: progress)
            points.append(point)
        }

        return points
    }

    // 计算圆角矩形路径上指定进度的点
    private func pointOnRoundedRectPath(rect: CGRect, cornerRadius: CGFloat, progress: Double) -> CGPoint {
        let perimeter = 2 * (rect.width + rect.height) - 8 * cornerRadius + 2 * .pi * cornerRadius
        let distance = progress * perimeter

        // 顶边
        let topLength = rect.width - 2 * cornerRadius
        if distance <= topLength {
            return CGPoint(x: rect.minX + cornerRadius + distance, y: rect.minY)
        }

        // 右上角
        let topRightArcLength = .pi * cornerRadius / 2
        if distance <= topLength + topRightArcLength {
            let arcProgress = (distance - topLength) / topRightArcLength
            let angle = -(.pi / 2) + arcProgress * (.pi / 2)
            return CGPoint(
                x: rect.maxX - cornerRadius + cos(angle) * cornerRadius,
                y: rect.minY + cornerRadius + sin(angle) * cornerRadius
            )
        }

        // 右边
        let rightLength = rect.height - 2 * cornerRadius
        if distance <= topLength + topRightArcLength + rightLength {
            let rightProgress = distance - topLength - topRightArcLength
            return CGPoint(x: rect.maxX, y: rect.minY + cornerRadius + rightProgress)
        }

        // 右下角
        let bottomRightArcLength = .pi * cornerRadius / 2
        if distance <= topLength + topRightArcLength + rightLength + bottomRightArcLength {
            let arcProgress = (distance - topLength - topRightArcLength - rightLength) / bottomRightArcLength
            let angle = arcProgress * (.pi / 2)
            return CGPoint(
                x: rect.maxX - cornerRadius + cos(angle) * cornerRadius,
                y: rect.maxY - cornerRadius + sin(angle) * cornerRadius
            )
        }

        // 底边
        let bottomLength = rect.width - 2 * cornerRadius
        if distance <= topLength + topRightArcLength + rightLength + bottomRightArcLength + bottomLength {
            let bottomProgress = distance - topLength - topRightArcLength - rightLength - bottomRightArcLength
            return CGPoint(x: rect.maxX - cornerRadius - bottomProgress, y: rect.maxY)
        }

        // 左下角
        let bottomLeftArcLength = .pi * cornerRadius / 2
        if distance <= topLength + topRightArcLength + rightLength + bottomRightArcLength + bottomLength + bottomLeftArcLength {
            let arcProgress = (distance - topLength - topRightArcLength - rightLength - bottomRightArcLength - bottomLength) / bottomLeftArcLength
            let angle = (.pi / 2) + arcProgress * (.pi / 2)
            return CGPoint(
                x: rect.minX + cornerRadius + cos(angle) * cornerRadius,
                y: rect.maxY - cornerRadius + sin(angle) * cornerRadius
            )
        }

        // 左边
        let leftLength = rect.height - 2 * cornerRadius
        if distance <= topLength + topRightArcLength + rightLength + bottomRightArcLength + bottomLength + bottomLeftArcLength + leftLength {
            let leftProgress = distance - topLength - topRightArcLength - rightLength - bottomRightArcLength - bottomLength - bottomLeftArcLength
            return CGPoint(x: rect.minX, y: rect.maxY - cornerRadius - leftProgress)
        }

        // 左上角
        let topLeftArcLength = .pi * cornerRadius / 2
        let arcProgress = (distance - topLength - topRightArcLength - rightLength - bottomRightArcLength - bottomLength - bottomLeftArcLength - leftLength) / topLeftArcLength
        let angle = .pi + arcProgress * (.pi / 2)
        return CGPoint(
            x: rect.minX + cornerRadius + cos(angle) * cornerRadius,
            y: rect.minY + cornerRadius + sin(angle) * cornerRadius
        )
    }
}

// 真正的屏幕边框路径，贴合屏幕边缘和圆角
struct ScreenBorderPath: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 使用传入的rect作为绘制区域
        let cornerRadius: CGFloat = 40 // iPhone的屏幕圆角半径
        let strokeWidth: CGFloat = 4
        let inset: CGFloat = strokeWidth / 2 // 避免边缘被裁切

        // 使用传入的rect尺寸
        let adjustedRect = rect.insetBy(dx: inset, dy: inset)

        // 从顶部左侧圆角后开始，顺时针绘制
        path.move(to: CGPoint(x: adjustedRect.minX + cornerRadius, y: adjustedRect.minY))

        // 顶边
        path.addLine(to: CGPoint(x: adjustedRect.maxX - cornerRadius, y: adjustedRect.minY))

        // 右上角圆角
        path.addArc(
            center: CGPoint(x: adjustedRect.maxX - cornerRadius, y: adjustedRect.minY + cornerRadius),
            radius: cornerRadius,
            startAngle: .degrees(-90),
            endAngle: .degrees(0),
            clockwise: false
        )

        // 右边
        path.addLine(to: CGPoint(x: adjustedRect.maxX, y: adjustedRect.maxY - cornerRadius))

        // 右下角圆角
        path.addArc(
            center: CGPoint(x: adjustedRect.maxX - cornerRadius, y: adjustedRect.maxY - cornerRadius),
            radius: cornerRadius,
            startAngle: .degrees(0),
            endAngle: .degrees(90),
            clockwise: false
        )

        // 底边
        path.addLine(to: CGPoint(x: adjustedRect.minX + cornerRadius, y: adjustedRect.maxY))

        // 左下角圆角
        path.addArc(
            center: CGPoint(x: adjustedRect.minX + cornerRadius, y: adjustedRect.maxY - cornerRadius),
            radius: cornerRadius,
            startAngle: .degrees(90),
            endAngle: .degrees(180),
            clockwise: false
        )

        // 左边
        path.addLine(to: CGPoint(x: adjustedRect.minX, y: adjustedRect.minY + cornerRadius))

        // 左上角圆角
        path.addArc(
            center: CGPoint(x: adjustedRect.minX + cornerRadius, y: adjustedRect.minY + cornerRadius),
            radius: cornerRadius,
            startAngle: .degrees(180),
            endAngle: .degrees(270),
            clockwise: false
        )

        return path
    }
}

// 预览
struct BorderProgressView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // 模拟应用背景
            Color.black.opacity(0.95)
                .ignoresSafeArea()

            VStack(spacing: 50) {
                Text("AI正在分析中...")
                    .font(.title2)
                    .foregroundColor(.white)

                Text("请稍候，马上就好")
                    .font(.body)
                    .foregroundColor(.gray)
            }

            // 全屏边框跑马灯效果
            BorderProgressView(isActive: true)
        }
        .preferredColorScheme(.dark)
    }
}
