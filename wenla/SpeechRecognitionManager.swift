//
//  SpeechRecognitionManager.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import Foundation
import Speech
import AVFoundation

class SpeechRecognitionManager: NSObject, ObservableObject {
    @Published var isRecording = false
    @Published var isAuthorized = false
    @Published var transcript = ""
    @Published var errorMessage: String?
    
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    
    override init() {
        super.init()
        setupSpeechRecognizer()
        requestPermissions()
    }
    
    // MARK: - 设置语音识别器
    private func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self
    }
    
    // MARK: - 请求权限
    private func requestPermissions() {
        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    self?.isAuthorized = true
                    print("🎤 语音识别权限已授权")
                case .denied, .restricted, .notDetermined:
                    self?.isAuthorized = false
                    self?.errorMessage = "语音识别权限被拒绝"
                    print("🎤 语音识别权限被拒绝")
                @unknown default:
                    self?.isAuthorized = false
                    print("🎤 语音识别权限状态未知")
                }
            }
        }
        
        // 请求麦克风权限
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                if !granted {
                    self?.errorMessage = "麦克风权限被拒绝"
                    print("🎤 麦克风权限被拒绝")
                }
            }
        }
    }
    
    // MARK: - 开始录音和识别
    func startRecording() {
        guard isAuthorized else {
            DispatchQueue.main.async {
                self.errorMessage = "语音识别权限未授权"
            }
            return
        }

        // 停止之前的任务
        stopRecording()

        // 检查语音识别器是否可用
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            DispatchQueue.main.async {
                self.errorMessage = "语音识别器不可用"
            }
            return
        }

        do {
            // 配置音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest else {
                DispatchQueue.main.async {
                    self.errorMessage = "无法创建识别请求"
                }
                return
            }

            recognitionRequest.shouldReportPartialResults = true

            // 配置音频引擎
            let inputNode = audioEngine.inputNode
            let recordingFormat = inputNode.outputFormat(forBus: 0)

            // 移除之前的tap
            inputNode.removeTap(onBus: 0)

            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak recognitionRequest] buffer, _ in
                recognitionRequest?.append(buffer)
            }

            // 开始识别任务
            recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                DispatchQueue.main.async {
                    if let result = result {
                        self?.transcript = result.bestTranscription.formattedString
                        print("🎤 识别结果: \(result.bestTranscription.formattedString)")
                    }

                    if let error = error {
                        self?.errorMessage = error.localizedDescription
                        self?.stopRecording()
                        print("🎤 识别错误: \(error.localizedDescription)")
                    }
                }
            }

            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()

            DispatchQueue.main.async {
                self.isRecording = true
                self.errorMessage = nil
                print("🎤 开始录音和语音识别")
            }

        } catch {
            DispatchQueue.main.async {
                self.errorMessage = error.localizedDescription
                print("🎤 启动录音失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 停止录音和识别
    func stopRecording() {
        // 停止音频引擎
        if audioEngine.isRunning {
            audioEngine.stop()
        }

        // 安全地移除tap
        do {
            audioEngine.inputNode.removeTap(onBus: 0)
        } catch {
            print("🎤 移除tap失败: \(error.localizedDescription)")
        }

        // 结束识别请求
        recognitionRequest?.endAudio()
        recognitionRequest = nil

        // 取消识别任务
        recognitionTask?.cancel()
        recognitionTask = nil

        DispatchQueue.main.async {
            self.isRecording = false
            print("🎤 停止录音和语音识别")
        }

        // 重置音频会话
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("🎤 重置音频会话失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 清空转录文本
    func clearTranscript() {
        transcript = ""
    }
    
    // MARK: - 获取完整转录文本
    func getFullTranscript() -> String {
        return transcript
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionManager: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.errorMessage = "语音识别服务不可用"
                self.stopRecording()
            }
        }
    }
}
