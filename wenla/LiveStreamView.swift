//
//  LiveStreamView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI
import AVFoundation
import UIKit
import UIKit

struct LiveStreamView: View {
    @EnvironmentObject var liveStreamManager: LiveStreamManager
    @StateObject private var cameraManager = CameraManager()
    @StateObject private var ecommerceManager = ECommerceManager()
    @StateObject private var speechManager = SpeechRecognitionManager()
    @ObservedObject var userSettings: UserSettings
    @State private var showingLikes: [LikeAnimation] = []
    @State private var autoLikeTimer: Timer?
    @State private var burstLikeTimer: Timer?
    @State private var showProductInput = false
    @State private var showLiveStreamSummary = false
    @State private var liveStreamStartTime: Date?
    @State private var likeAnimationTrigger = 0
    @State private var customerQuestionTrigger = 0
    @State private var showReportGenerating = false
    @State private var reportGenerationProgress: Double = 0.0
    @State private var reportGenerationTimer: Timer?

    // 氛围表情包相关状态
    @State private var atmosphereEmojis: [AtmosphereEmoji] = []
    @State private var atmosphereTimer: Timer?

    // 添加返回主页面的回调
    @Environment(\.presentationMode) var presentationMode
    var onClose: (() -> Void)?

    // 初始化方法
    init(userSettings: UserSettings = UserSettings(), onClose: (() -> Void)? = nil) {
        self.userSettings = userSettings
        self.onClose = onClose
    }

    var body: some View {
        ZStack {
            // 背景摄像头区域
            backgroundCameraArea

            // 顶部状态栏
            topStatusBar

            // 左侧弹幕区域
            leftChatArea

            // 右侧互动区域
            rightInteractionArea

            // 底部控制栏
            bottomControlBar

            // 点赞动画
            likeAnimations

            // 氛围表情包动画
            atmosphereEmojiAnimations

            // AI分析跑马灯效果
            if ecommerceManager.isAnalyzing {
                BorderProgressView(isActive: true)
                    .transition(.opacity)
                    .animation(.easeInOut(duration: 0.5), value: ecommerceManager.isAnalyzing)
            }

            // 报告生成进度动画
            if showReportGenerating {
                reportGeneratingOverlay
            }
        }
        .ignoresSafeArea()
        .onAppear {
            setupLiveStream()
            // 进入直播页面时确保不会息屏
            UIApplication.shared.isIdleTimerDisabled = true
            print("📺 进入直播页面，已禁用自动锁屏")

            // 监听内存警告
            NotificationCenter.default.addObserver(
                forName: UIApplication.didReceiveMemoryWarningNotification,
                object: nil,
                queue: .main
            ) { _ in
                print("⚠️ 收到内存警告，正在清理...")
                handleMemoryWarning()
            }

            // 监听应用进入后台
            NotificationCenter.default.addObserver(
                forName: UIApplication.didEnterBackgroundNotification,
                object: nil,
                queue: .main
            ) { _ in
                print("📱 应用进入后台，清理任务")
                ecommerceManager.cleanup()
            }
        }
        .onDisappear {
            cleanupLiveStream()
            // 离开直播页面时可以根据需要恢复息屏（这里保持禁用状态，由App级别控制）
            print("📺 离开直播页面")

            // 清理电商管理器的任务
            Task { @MainActor in
                ecommerceManager.cleanup()
            }

            // 移除所有通知监听器
            NotificationCenter.default.removeObserver(
                self,
                name: UIApplication.didReceiveMemoryWarningNotification,
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: UIApplication.didEnterBackgroundNotification,
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: .likeGenerated,
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: .newQuestionGenerated,
                object: nil
            )
        }
        .sheet(isPresented: $showProductInput) {
            ProductInputView(isPresented: $showProductInput, ecommerceManager: ecommerceManager)
        }
        .sheet(isPresented: $showLiveStreamSummary) {
            if let summary = ecommerceManager.liveStreamSummary {
                LiveStreamSummaryView(
                    isPresented: $showLiveStreamSummary,
                    summary: summary,
                    onComplete: {
                        // 用户查看完总结后返回主页面
                        onClose?()
                    }
                )
            }
        }
    }
    
    // MARK: - 背景摄像头区域
    private var backgroundCameraArea: some View {
        ZStack {
            if cameraManager.isCameraActive {
                // 模拟摄像头预览
                #if targetEnvironment(simulator)
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.black.opacity(0.3),
                                Color.gray.opacity(0.2),
                                Color.black.opacity(0.4)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .onAppear {
                        print("📱 模拟器摄像头预览已显示")
                    }
                #else
                // 真实摄像头预览
                CameraPreviewView(cameraManager: cameraManager)
                    .onAppear {
                        print("📷 真实摄像头预览已显示")
                    }
                #endif
            } else {
                // 默认背景或错误显示
                ZStack {
                    Rectangle()
                        .fill(Color.black)

                    if let errorMessage = cameraManager.errorMessage {
                        VStack(spacing: 16) {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 48))
                                .foregroundColor(.white.opacity(0.6))

                            Text(errorMessage)
                                .font(.body)
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)

                            Button("重试") {
                                cameraManager.startCamera()
                            }
                            .foregroundColor(.blue)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 8)
                            .background(Color.white.opacity(0.2))
                            .cornerRadius(8)
                        }
                    } else {
                        VStack(spacing: 12) {
                            Image(systemName: "camera")
                                .font(.system(size: 48))
                                .foregroundColor(.white.opacity(0.4))

                            Text("正在启动摄像头...")
                                .font(.body)
                                .foregroundColor(.white.opacity(0.6))
                        }
                    }
                }
                .onAppear {
                    print("⚫ 显示默认背景，摄像头未启动")
                }
            }
        }
        .onAppear {
            print("🎬 backgroundCameraArea onAppear - 摄像头状态: \(cameraManager.isCameraActive)")
        }
    }
    
    // MARK: - 顶部状态栏
    private var topStatusBar: some View {
        VStack {
            HStack {
                // 左侧主播信息
                HStack(spacing: 8) {
                    // 主播头像 - 使用用户设置的头像
                    if userSettings.isUsingCustomImageAvatar, let uiImage = userSettings.getSafeAvatarImage() {
                        // 显示自定义图片头像
                        Circle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 40, height: 40)
                                    .clipShape(Circle())
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.blue.opacity(0.6), lineWidth: 1)
                            )
                    } else if userSettings.isUsingDefaultAvatar {
                        // 使用默认头像
                        Circle()
                            .fill(Color.gray.opacity(0.6))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Text(userSettings.displayAvatar)
                                    .font(.system(size: 18))
                                    .foregroundColor(.white)
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 0.5) // 细线灰色边框
                            )
                    } else {
                        // 使用用户设置的emoji头像
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.green, Color.blue]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 40, height: 40)
                            .overlay(
                                Text(userSettings.displayAvatar)
                                    .font(.system(size: 18))
                                    .foregroundColor(.white)
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 0.5) // 细线灰色边框
                            )
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text(userSettings.username)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)

                        Text("\(liveStreamManager.stats.formattedLikeCount)本场点赞")
                            .font(.system(size: 10))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.black.opacity(0.2)) // 提高背景透明度
                .cornerRadius(20)

                Spacer()

                // 右侧功能按钮
                HStack(spacing: 6) { // 减少间距让元素更紧凑
                    // 观众头像组 - 往后靠
                    HStack(spacing: -8) {
                        ForEach(0..<3, id: \.self) { index in
                            let imageName = "p\(index + 1)" // p1, p2, p3
                            Circle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 28, height: 28)
                                .overlay(
                                    Image(imageName)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: 28, height: 28)
                                        .clipShape(Circle())
                                )
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.6), lineWidth: 1) // 白色边框更明显
                                )
                        }
                    }

                    // 观看人数 - 使用绝对定位和固定宽度避免跳动
                    ZStack {
                        // 背景容器，固定宽度
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.2))
                            .frame(width: 65, height: 24) // 稍微增加宽度以适应"10万+"

                        // 数字文本，绝对定位
                        Text(liveStreamManager.stats.formattedViewerCount)
                            .font(.system(size: 12, weight: .medium, design: .monospaced)) // 使用等宽字体
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .frame(width: 61, height: 20) // 固定文本区域
                            .clipped() // 裁剪超出部分
                    }

                    // 关闭按钮
                    Button(action: {
                        closeLiveStreamAndReturn()
                    }) {
                        Image(systemName: "power")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .padding(8)
                            .background(Color.black.opacity(0.2)) // 提高透明度
                            .clipShape(Circle())
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 50) // 避开状态栏

            Spacer()
        }
    }

    // MARK: - 左侧弹幕区域
    private var leftChatArea: some View {
        VStack(alignment: .leading) {
            Spacer()

            // 向下移动弹幕位置
            VStack(alignment: .leading, spacing: 0) {
                Spacer()
                    .frame(height: 80) // 增加顶部间距，让弹幕向下移动

                chatMessagesList
                    .frame(maxWidth: .infinity, alignment: .leading) // 确保左对齐

                Spacer()
                    .frame(height: 80) // 为底部控制栏留空间
            }
            .padding(.leading, 8) // 左边距设为8，更贴近边缘
            .padding(.trailing, 60) // 右边距增加，避免与右侧按钮重叠
        }
    }

    // MARK: - 弹幕消息列表
    private var chatMessagesList: some View {
        VStack(alignment: .leading, spacing: 6) {
            ForEach(Array(liveStreamManager.chatMessages.suffix(8).enumerated()), id: \.element.id) { index, message in
                HStack {
                    chatMessageRow(message: message, index: index)
                        .transition(.asymmetric(
                            insertion: .move(edge: .leading)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.8)),
                            removal: .move(edge: .leading)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.8))
                        ))
                    Spacer() // 推动弹幕到最左侧
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .animation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.05), value: liveStreamManager.chatMessages.map { $0.id })
    }

    // MARK: - 单条弹幕消息
    private func chatMessageRow(message: ChatMessage, index: Int) -> some View {
        return HStack(alignment: .top, spacing: 6) {
            // 等级标识 - 大圆角矩形（移除动画效果）
            HStack(spacing: 3) {
                Image(systemName: message.avatarIcon)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white)

                Text("\(message.userLevel)")
                    .font(.system(size: 11, weight: .bold))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 6)
            .padding(.vertical, 3)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.blue.opacity(0.8),
                                Color.purple.opacity(0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )

            // 用户名和消息内容 - 支持换行对齐
            chatMessageContent(message: message)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.black.opacity(0.4),
                            Color.black.opacity(0.3)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .shadow(color: .black.opacity(0.2), radius: 3, x: 0, y: 1)
        )
    }

    // MARK: - 弹幕内容（支持换行对齐）
    private func chatMessageContent(message: ChatMessage) -> some View {
        return VStack(alignment: .leading, spacing: 0) {
            // 使用自定义的文本布局来实现从标签位置开始换行
            TagAlignedText(
                username: message.username,
                message: message.message
            )
        }
    }

    // MARK: - 右侧互动区域
    private var rightInteractionArea: some View {
        VStack {
            Spacer()

            // 隐藏的点赞触发区域（保持位置用于飘心起点）
            HStack {
                Spacer()

                VStack(spacing: 16) {
                    // 隐藏的点赞区域，只用于定位飘心起点
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: 50, height: 50)
                }
                .padding(.trailing, 16)
            }
            .padding(.bottom, 120) // 为底部控制栏留空间
        }
    }

    // MARK: - 底部控制栏
    private var bottomControlBar: some View {
        VStack {
            Spacer()



            // 底部功能栏
            HStack(spacing: 0) {
                // PK按钮
                Button(action: {}) {
                    VStack(spacing: 4) {
                        Text("PK")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.pink)
                        Text("PK")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)

                // 连线按钮
                Button(action: {}) {
                    VStack(spacing: 4) {
                        Image(systemName: "infinity")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                        Text("连线")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)

                // 互动按钮
                Button(action: {}) {
                    VStack(spacing: 4) {
                        Image(systemName: "person.2")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                        Text("互动")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)

                // 装饰按钮
                Button(action: {}) {
                    VStack(spacing: 4) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                        Text("装饰")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)

                // 卖货按钮
                Button(action: {
                    showProductInput = true
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "bag")
                            .font(.system(size: 18))
                            .foregroundColor(ecommerceManager.currentProduct != nil ? .green : .white)
                        Text("卖货")
                            .font(.system(size: 10))
                            .foregroundColor(ecommerceManager.currentProduct != nil ? .green : .white)
                    }
                }
                .frame(maxWidth: .infinity)

                // 摄像头切换按钮
                Button(action: {
                    switchCamera()
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: cameraManager.currentCameraPosition == .front ? "camera.rotate" : "camera.rotate.fill")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                        Text(cameraManager.currentCameraPosition == .front ? "后置" : "前置")
                            .font(.system(size: 10))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.black.opacity(0.9))
        }
    }

    // MARK: - 点赞动画
    private var likeAnimations: some View {
        ZStack {
            ForEach(showingLikes, id: \.id) { like in
                Group {
                    // 随机选择不同的心形样式
                    if like.heartType == 0 {
                        Text("❤️")
                            .font(.system(size: like.size))
                    } else if like.heartType == 1 {
                        Text("💖")
                            .font(.system(size: like.size))
                    } else if like.heartType == 2 {
                        Text("💕")
                            .font(.system(size: like.size))
                    } else {
                        Text("💗")
                            .font(.system(size: like.size))
                    }
                }
                .position(x: like.x, y: like.y)
                .opacity(like.opacity)
                .scaleEffect(like.scale)
                .rotationEffect(.degrees(like.rotation))
            }
        }
    }

    // MARK: - 氛围表情包动画
    private var atmosphereEmojiAnimations: some View {
        ZStack {
            ForEach(atmosphereEmojis, id: \.id) { emoji in
                Text(emoji.emoji)
                    .font(.system(size: 24))
                    .scaleEffect(emoji.scale)
                    .opacity(emoji.opacity)
                    .rotationEffect(.degrees(emoji.rotation))
                    .position(x: emoji.x, y: emoji.y)
                    .animation(.none, value: emoji.id) // 禁用自动动画，使用手动控制
            }
        }
        .allowsHitTesting(false) // 不阻挡用户交互
    }

    // MARK: - 报告生成进度动画
    private var reportGeneratingOverlay: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.7)
                .ignoresSafeArea()

            VStack(spacing: 24) {
                // 动画图标
                ZStack {
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 4)
                        .frame(width: 80, height: 80)

                    Circle()
                        .trim(from: 0, to: reportGenerationProgress)
                        .stroke(Color.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 0.5), value: reportGenerationProgress)

                    Image(systemName: "chart.bar.doc.horizontal")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                }

                VStack(spacing: 8) {
                    Text("正在生成直播报告...")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    Text("请稍候，AI正在分析您的直播表现")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)

                    // 进度百分比
                    Text("\(Int(reportGenerationProgress * 100))%")
                        .font(.headline)
                        .foregroundColor(.blue)
                        .fontWeight(.bold)
                }

                // 进度条
                VStack(spacing: 8) {
                    HStack {
                        Text("分析进度")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Spacer()
                        Text(getProgressStageText())
                            .font(.caption)
                            .foregroundColor(.blue)
                    }

                    ProgressView(value: reportGenerationProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }
                .padding(.horizontal, 40)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .padding(.horizontal, 40)
        }
    }



    // MARK: - 添加客户问题到弹幕
    private func addCustomerQuestionToChat(_ question: CustomerQuestion) {
        // 安全检查：确保问题内容不为空
        guard !question.question.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("⚠️ 跳过空问题")
            return
        }

        // 生成随机用户名
        let usernames = ["品质达人", "省钱专家", "急需用户", "体验达人", "时尚小姐", "服务关注者", "实用主义者", "性价比控"]
        let randomUsername = usernames.randomElement() ?? "观众"

        let chatMessage = ChatMessage(
            username: randomUsername,
            message: question.question,
            timestamp: Date(),
            messageType: .normal,
            userLevel: Int.random(in: 1...10)
        )

        // 添加到弹幕列表
        liveStreamManager.chatMessages.append(chatMessage)

        // 限制弹幕数量
        if liveStreamManager.chatMessages.count > 20 {
            liveStreamManager.chatMessages.removeFirst()
        }

        // 触发弹幕动画效果
        withAnimation(.easeInOut(duration: 0.3)) {
            // 弹幕会自动显示新的聊天消息
        }
    }

    // MARK: - 添加点赞动画
    private func addLikeAnimation() {
        // 生成1-2个心形，模拟连续点击效果
        let heartCount = Int.random(in: 1...2)

        for i in 0..<heartCount {
            let delay = Double(i) * 0.05 // 很短的错开时间

            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                let screenWidth = UIScreen.main.bounds.width
                let screenHeight = UIScreen.main.bounds.height

                // 固定从右下角按钮位置开始（模拟按钮位置）
                let buttonX = screenWidth - 16 - 25 // 右边距16 + 按钮宽度一半25
                let buttonY = screenHeight - 120 - 60 // 底部距离120 + 按钮区域60

                let newLike = LikeAnimation(
                    id: UUID(),
                    x: buttonX + CGFloat.random(in: -10...10), // 按钮位置附近小范围随机
                    y: buttonY + CGFloat.random(in: -10...10),
                    opacity: 1.0,
                    scale: 0.8,
                    size: CGFloat.random(in: 24...36),
                    heartType: Int.random(in: 0...3),
                    rotation: 0
                )

                showingLikes.append(newLike)

                // 更自然的飘动动画
                withAnimation(.easeOut(duration: 2.5)) {
                    if let index = showingLikes.firstIndex(where: { $0.id == newLike.id }) {
                        // 向上飘动，带有优美的弧线轨迹
                        showingLikes[index].y -= CGFloat.random(in: 250...350)
                        showingLikes[index].x += CGFloat.random(in: -80...30) // 稍微向左飘
                        showingLikes[index].opacity = 0
                        showingLikes[index].scale = CGFloat.random(in: 1.0...1.5)
                        showingLikes[index].rotation = Double.random(in: -20...20)
                    }
                }

                // 2.5秒后移除
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                    showingLikes.removeAll { $0.id == newLike.id }
                }
            }
        }
    }

    // MARK: - 自动点赞定时器
    private func startAutoLikeTimer() {
        autoLikeTimer = Timer.scheduledTimer(withTimeInterval: Double.random(in: 0.3...1.2), repeats: false) { _ in
            // 连续触发点赞
            addLikeAnimation()

            // 重新设置下一次定时器
            startAutoLikeTimer()
        }
    }

    private func stopAutoLikeTimer() {
        autoLikeTimer?.invalidate()
        autoLikeTimer = nil
    }

    // MARK: - 爆发式点赞定时器
    private func startBurstLikeTimer() {
        burstLikeTimer = Timer.scheduledTimer(withTimeInterval: Double.random(in: 3.0...8.0), repeats: false) { _ in
            // 爆发式连续点赞
            triggerBurstLikes()

            // 重新设置下一次爆发
            startBurstLikeTimer()
        }
    }

    private func stopBurstLikeTimer() {
        burstLikeTimer?.invalidate()
        burstLikeTimer = nil
    }



    // MARK: - 获取进度阶段文本
    private func getProgressStageText() -> String {
        switch reportGenerationProgress {
        case 0.0..<0.2:
            return "准备数据..."
        case 0.2..<0.4:
            return "分析直播内容..."
        case 0.4..<0.6:
            return "评估表现指标..."
        case 0.6..<0.8:
            return "生成优化建议..."
        case 0.8..<1.0:
            return "完善报告..."
        default:
            return "生成完成"
        }
    }

    // MARK: - 切换摄像头
    private func switchCamera() {
        print("📷 用户点击切换摄像头")

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 切换摄像头
        cameraManager.switchCamera()

        print("📷 摄像头切换完成，当前位置: \(cameraManager.currentCameraPosition == .front ? "前置" : "后置")")
    }

    // MARK: - 内存警告处理
    private func handleMemoryWarning() {
        print("🧹 开始内存清理...")

        // 清理聊天消息历史，只保留最近的50条
        if liveStreamManager.chatMessages.count > 50 {
            let keepCount = 50
            let removeCount = liveStreamManager.chatMessages.count - keepCount
            liveStreamManager.chatMessages.removeFirst(removeCount)
            print("🧹 清理了 \(removeCount) 条历史聊天消息")
        }

        // 清理电商管理器的任务和缓存
        ecommerceManager.cleanup()
        print("🧹 清理了电商管理器任务")

        // 强制垃圾回收
        DispatchQueue.global(qos: .background).async {
            // 在后台线程执行内存清理
            print("🧹 执行后台内存清理")
        }
    }

    // MARK: - 生成直播总结并显示（用于关闭直播时）
    private func generateLiveStreamSummaryForDisplay() {
        guard let startTime = liveStreamStartTime,
              ecommerceManager.currentProduct != nil else {
            print("📊 跳过直播总结生成：缺少必要数据")
            return
        }

        print("📊 开始生成直播总结...")

        // 显示进度动画
        showReportGenerating = true
        reportGenerationProgress = 0.0

        // 启动进度模拟定时器
        startReportGenerationProgress()

        _ = Date().timeIntervalSince(startTime)
        _ = speechManager.getFullTranscript()
        _ = "商品链接" // 实际应该保存用户输入的链接

        Task {
            await ecommerceManager.generateLiveStreamSummary()

            DispatchQueue.main.async {
                print("📊 直播总结生成完成，显示总结页面")
                self.stopReportGenerationProgress()
                self.showReportGenerating = false
                self.showLiveStreamSummary = true
            }
        }
    }

    // MARK: - 生成直播总结（带回调版本）
    private func generateLiveStreamSummaryWithCallback(completion: @escaping () -> Void) {
        guard let startTime = liveStreamStartTime,
              ecommerceManager.currentProduct != nil else {
            print("📊 跳过直播总结生成：缺少必要数据")
            completion()
            return
        }

        print("📊 开始生成直播总结...")
        let duration = Date().timeIntervalSince(startTime)
        let transcript = speechManager.getFullTranscript()
        let productUrl = "商品链接" // 实际应该保存用户输入的链接

        Task {
            await ecommerceManager.generateLiveStreamSummary()

            DispatchQueue.main.async {
                print("📊 直播总结生成完成，显示总结页面")
                self.showLiveStreamSummary = true

                // 延迟执行回调，让用户有时间查看总结
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    completion()
                }
            }
        }
    }

    // MARK: - 生成直播总结（异步版本，不阻塞UI）
    private func generateLiveStreamSummaryAsync() {
        guard let startTime = liveStreamStartTime,
              ecommerceManager.currentProduct != nil else {
            print("📊 跳过直播总结生成：缺少必要数据")
            return
        }

        print("📊 开始生成直播总结...")
        let duration = Date().timeIntervalSince(startTime)
        let transcript = speechManager.getFullTranscript()
        let productUrl = "商品链接" // 实际应该保存用户输入的链接

        Task {
            await ecommerceManager.generateLiveStreamSummary()
            print("📊 直播总结生成完成")
        }
    }

    // MARK: - 生成直播总结（保留原方法）
    private func generateLiveStreamSummary() {
        guard let startTime = liveStreamStartTime,
              ecommerceManager.currentProduct != nil else {
            return
        }

        let duration = Date().timeIntervalSince(startTime)
        let transcript = speechManager.getFullTranscript()
        let productUrl = "商品链接" // 实际应该保存用户输入的链接

        Task {
            await ecommerceManager.generateLiveStreamSummary()

            DispatchQueue.main.async {
                self.showLiveStreamSummary = true
            }
        }
    }

    // MARK: - 设置直播环境
    private func setupLiveStream() {
        // 记录直播开始时间
        liveStreamStartTime = Date()

        // 清理之前的通知观察者
        NotificationCenter.default.removeObserver(self, name: .likeGenerated, object: nil)
        NotificationCenter.default.removeObserver(self, name: .newQuestionGenerated, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIApplication.didReceiveMemoryWarningNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIApplication.didEnterBackgroundNotification, object: nil)

        // 设置LiveStreamManager和ECommerceManager的关联
        liveStreamManager.ecommerceManager = ecommerceManager

        // 启动直播管理器
        if !liveStreamManager.isLive {
            liveStreamManager.startLiveStream()
        }

        // 自动启动前置摄像头
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            print("🎬 准备启动摄像头，当前状态: \(self.cameraManager.isCameraActive)")
            print("🎬 摄像头权限状态: \(self.cameraManager.isAuthorized)")

            if !self.cameraManager.isCameraActive {
                print("🎬 调用摄像头启动")
                self.cameraManager.startCamera()
            } else {
                print("🎬 摄像头已经启动")
            }
        }

        // 延迟启动语音识别，等待权限确认
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.speechManager.isAuthorized {
                self.speechManager.startRecording()
            }
        }

        // 设置通知观察者
        setupNotificationObservers()

        // 启动定时器
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.startAutoLikeTimer()
            self.startBurstLikeTimer()
            self.startAtmosphereEmojiTimer() // 启动氛围表情包定时器
        }
    }

    // MARK: - 设置通知观察者
    private func setupNotificationObservers() {
        // 监听点赞通知
        NotificationCenter.default.addObserver(
            forName: .likeGenerated,
            object: nil,
            queue: .main
        ) { _ in
            DispatchQueue.main.async {
                likeAnimationTrigger += 1
                addLikeAnimation()
            }
        }

        // 监听新问题生成通知
        NotificationCenter.default.addObserver(
            forName: .newQuestionGenerated,
            object: nil,
            queue: .main
        ) { notification in
            if let question = notification.object as? CustomerQuestion {
                DispatchQueue.main.async {
                    addCustomerQuestionToChat(question)
                }
            }
        }
    }

    // MARK: - 关闭直播并返回主页面
    private func closeLiveStreamAndReturn() {
        print("🔴 开始关闭直播...")

        // 立即停止直播管理器
        liveStreamManager.stopLiveStream()

        // 快速清理关键资源
        quickCleanup()

        // 生成直播总结并显示（如果有产品数据）
        if liveStreamStartTime != nil && ecommerceManager.currentProduct != nil {
            generateLiveStreamSummaryForDisplay()
        } else {
            // 没有产品数据，直接返回主页面
            onClose?()
        }

        print("🔴 直播关闭流程启动")
    }

    // MARK: - 关闭直播（保留原方法用于其他地方调用）
    private func closeLiveStream() {
        // 停止直播管理器
        liveStreamManager.stopLiveStream()

        // 清理直播环境
        cleanupLiveStream()
    }

    // MARK: - 启动报告生成进度模拟
    private func startReportGenerationProgress() {
        reportGenerationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            DispatchQueue.main.async {
                if self.reportGenerationProgress < 0.9 {
                    // 模拟进度增长，但不要达到100%，等待实际完成
                    let increment = Double.random(in: 0.01...0.03)
                    self.reportGenerationProgress = min(self.reportGenerationProgress + increment, 0.9)
                }
            }
        }
    }

    // MARK: - 停止报告生成进度模拟
    private func stopReportGenerationProgress() {
        reportGenerationTimer?.invalidate()
        reportGenerationTimer = nil
        reportGenerationProgress = 1.0
    }

    // MARK: - 快速清理（立即执行的关键清理）
    private func quickCleanup() {
        print("🧹 执行快速清理...")

        // 停止定时器（必须在主线程）
        stopAutoLikeTimer()
        stopBurstLikeTimer()
        stopReportGenerationProgress()

        // 停止摄像头
        cameraManager.stopCamera()

        // 停止语音识别
        speechManager.stopRecording()

        // 清理通知观察者
        NotificationCenter.default.removeObserver(self)

        print("🧹 快速清理完成")
    }

    // MARK: - 后台清理和分析
    private func backgroundCleanupAndAnalysis() {
        print("🔄 开始后台清理和分析...")

        // 生成直播总结（异步执行，不阻塞UI）
        generateLiveStreamSummaryAsync()

        print("🔄 后台清理和分析完成")
    }

    // MARK: - 清理直播环境（保留原方法）
    private func cleanupLiveStream() {
        // 停止定时器
        stopAutoLikeTimer()
        stopBurstLikeTimer()
        stopAtmosphereEmojiTimer() // 停止氛围表情包定时器

        // 停止摄像头
        cameraManager.stopCamera()

        // 停止语音识别
        speechManager.stopRecording()

        // 清理通知观察者
        NotificationCenter.default.removeObserver(self)

        // 生成直播总结
        generateLiveStreamSummary()
    }

    // MARK: - 爆发式点赞效果
    private func triggerBurstLikes() {
        let burstCount = Int.random(in: 5...12) // 一次爆发5-12个爱心

        for i in 0..<burstCount {
            let delay = Double(i) * 0.1 // 每个爱心间隔0.1秒

            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                addLikeAnimation()
            }
        }
    }

    // MARK: - 氛围表情包相关方法

    // 启动氛围表情包定时器
    private func startAtmosphereEmojiTimer() {
        atmosphereTimer = Timer.scheduledTimer(withTimeInterval: Double.random(in: 0.3...1.0), repeats: false) { _ in
            // 生成2-5个表情包，增加密度
            let emojiCount = Int.random(in: 2...5)

            for i in 0..<emojiCount {
                // 添加微小的延迟，让表情包不会完全同时出现，更自然
                DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.05) {
                    addAtmosphereEmoji()
                }
            }

            // 重新设置下一次定时器
            startAtmosphereEmojiTimer()
        }
    }

    // 停止氛围表情包定时器
    private func stopAtmosphereEmojiTimer() {
        atmosphereTimer?.invalidate()
        atmosphereTimer = nil
    }

    // 添加氛围表情包动画
    private func addAtmosphereEmoji() {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height

        // 随机选择表情包类型
        let emojiType: EmojiType = [.celebration, .shopping, .support].randomElement() ?? .celebration
        let emoji = AtmosphereEmojiData.getRandomEmoji(type: emojiType)

        // 随机选择动画类型
        let animationType: AtmosphereAnimationType = [.float, .bounce, .spin, .wave].randomElement() ?? .float

        // 从右下角区域开始，但有一定随机性
        let startX = screenWidth - CGFloat.random(in: 60...120)
        let startY = screenHeight - CGFloat.random(in: 100...200)

        let newEmoji = AtmosphereEmoji(
            emoji: emoji,
            x: startX,
            y: startY,
            animationType: animationType
        )

        atmosphereEmojis.append(newEmoji)

        // 根据动画类型执行不同的动画
        switch animationType {
        case .float:
            animateFloatEmoji(newEmoji)
        case .bounce:
            animateBounceEmoji(newEmoji)
        case .spin:
            animateSpinEmoji(newEmoji)
        case .wave:
            animateWaveEmoji(newEmoji)
        }

        // 2.8秒后移除表情包，配合更快的生成速度
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.8) {
            atmosphereEmojis.removeAll { $0.id == newEmoji.id }
        }
    }

    // 漂浮动画 - 更丝滑的效果
    private func animateFloatEmoji(_ emoji: AtmosphereEmoji) {
        // 添加初始的微小弹跳效果
        withAnimation(.interpolatingSpring(stiffness: 400, damping: 15, initialVelocity: 2)) {
            if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                atmosphereEmojis[index].scale *= 1.1
            }
        }

        // 主要的漂浮动画，使用更丝滑的曲线
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.timingCurve(0.25, 0.1, 0.25, 1.0, duration: 2.8)) {
                if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                    atmosphereEmojis[index].y -= CGFloat.random(in: 250...400)
                    atmosphereEmojis[index].x += CGFloat.random(in: -60...60)
                    atmosphereEmojis[index].opacity = 0
                    atmosphereEmojis[index].scale *= 1.3
                    atmosphereEmojis[index].rotation = Double.random(in: -15...15)
                }
            }
        }
    }

    // 弹跳动画 - 更有活力的丝滑效果
    private func animateBounceEmoji(_ emoji: AtmosphereEmoji) {
        // 第一阶段：快速弹跳放大
        withAnimation(.interpolatingSpring(stiffness: 500, damping: 12, initialVelocity: 3)) {
            if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                atmosphereEmojis[index].scale *= 1.6
                atmosphereEmojis[index].rotation = Double.random(in: -10...10)
            }
        }

        // 第二阶段：小幅回弹
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.interpolatingSpring(stiffness: 400, damping: 15)) {
                if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                    atmosphereEmojis[index].scale *= 0.9
                }
            }
        }

        // 第三阶段：丝滑上升消失
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.timingCurve(0.2, 0.0, 0.2, 1.0, duration: 2.4)) {
                if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                    atmosphereEmojis[index].y -= CGFloat.random(in: 200...320)
                    atmosphereEmojis[index].x += CGFloat.random(in: -50...50)
                    atmosphereEmojis[index].opacity = 0
                    atmosphereEmojis[index].scale *= 0.7
                    atmosphereEmojis[index].rotation += Double.random(in: -20...20)
                }
            }
        }
    }

    // 旋转动画 - 更丝滑的旋转效果
    private func animateSpinEmoji(_ emoji: AtmosphereEmoji) {
        // 初始缩放效果
        withAnimation(.interpolatingSpring(stiffness: 350, damping: 12)) {
            if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                atmosphereEmojis[index].scale *= 1.15
            }
        }

        // 主要的旋转上升动画，使用更丝滑的曲线
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.timingCurve(0.25, 0.46, 0.45, 0.94, duration: 2.7)) {
                if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                    atmosphereEmojis[index].rotation = Double.random(in: 270...450) // 更多旋转变化
                    atmosphereEmojis[index].y -= CGFloat.random(in: 220...350)
                    atmosphereEmojis[index].x += CGFloat.random(in: -70...70)
                    atmosphereEmojis[index].opacity = 0
                    atmosphereEmojis[index].scale *= 1.2
                }
            }
        }
    }

    // 波浪动画 - 更丝滑的波浪效果
    private func animateWaveEmoji(_ emoji: AtmosphereEmoji) {
        // 初始轻微缩放
        withAnimation(.interpolatingSpring(stiffness: 300, damping: 15)) {
            if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                atmosphereEmojis[index].scale *= 1.1
            }
        }

        let duration = 2.8
        let steps = 35 // 增加步数使动画更丝滑
        let stepDuration = duration / Double(steps)
        let amplitude = 40.0 // 增加波浪幅度
        let frequency = 3.5 // 调整波浪频率

        for i in 0..<steps {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * stepDuration) {
                if let index = atmosphereEmojis.firstIndex(where: { $0.id == emoji.id }) {
                    let progress = Double(i) / Double(steps)
                    let waveOffset = sin(progress * .pi * frequency) * amplitude * (1.0 - progress * 0.3)
                    let rotationOffset = cos(progress * .pi * frequency * 2) * 15

                    withAnimation(.timingCurve(0.25, 0.1, 0.25, 1.0, duration: stepDuration)) {
                        atmosphereEmojis[index].x += CGFloat(waveOffset / Double(steps))
                        atmosphereEmojis[index].y -= CGFloat(320 / Double(steps))
                        atmosphereEmojis[index].opacity = 1.0 - progress
                        atmosphereEmojis[index].rotation += rotationOffset / Double(steps)
                        atmosphereEmojis[index].scale *= (1.0 + progress * 0.3) // 逐渐放大
                    }
                }
            }
        }
    }
}

// MARK: - 点赞动画数据结构
struct LikeAnimation {
    let id: UUID
    var x: CGFloat
    var y: CGFloat
    var opacity: Double
    var scale: CGFloat
    var size: CGFloat
    var heartType: Int
    var rotation: Double
}

// MARK: - 通知扩展
extension Notification.Name {
    static let likeGenerated = Notification.Name("likeGenerated")
    static let newQuestionGenerated = Notification.Name("newQuestionGenerated")
}

// MARK: - 标签对齐文本组件
struct TagAlignedText: View {
    let username: String
    let message: String

    var body: some View {
        // 使用 Text 的 AttributedString 来实现换行对齐
        VStack(alignment: .leading, spacing: 0) {
            Text(createAttributedString())
                .lineLimit(nil)
                .multilineTextAlignment(.leading)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    private func createAttributedString() -> AttributedString {
        var result = AttributedString()

        // 用户名部分
        var usernameAttr = AttributedString(username)
        usernameAttr.font = .systemFont(ofSize: 13, weight: .bold)
        usernameAttr.foregroundColor = Color(red: 156/255, green: 226/255, blue: 250/255)

        // 冒号部分
        var colonAttr = AttributedString(": ")
        colonAttr.font = .systemFont(ofSize: 13, weight: .medium)
        colonAttr.foregroundColor = Color.white.opacity(0.8)

        // 消息内容部分
        var messageAttr = AttributedString(message)
        messageAttr.font = .systemFont(ofSize: 15, weight: .regular) // 增加2点字体大小
        messageAttr.foregroundColor = Color.white

        // 组合所有部分
        result.append(usernameAttr)
        result.append(colonAttr)
        result.append(messageAttr)

        return result
    }
}

// MARK: - 字符串宽度计算扩展
extension String {
    func widthOfString(usingFont font: UIFont) -> CGFloat {
        let fontAttributes = [NSAttributedString.Key.font: font]
        let size = self.size(withAttributes: fontAttributes)
        return size.width
    }
}
