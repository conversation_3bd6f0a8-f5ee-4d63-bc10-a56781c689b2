//
//  ECommerceManager.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import Foundation
import SwiftUI

// MARK: - 自定义错误类型
struct TimeoutError: Error {
    let message = "操作超时"
}

// MARK: - 产品信息
struct ProductInfo {
    let name: String
    let description: String
    let price: String
    let imageURL: String?
    let features: [String]
    let category: String

    // 添加便利初始化器以支持旧的调用方式
    init(name: String, description: String, price: String, imageURL: String? = nil, features: [String], category: String) {
        self.name = name
        self.description = description
        self.price = price
        self.imageURL = imageURL
        self.features = features
        self.category = category
    }

    // 支持旧的参数名称
    init(title: String, description: String, price: String, imageURL: String? = nil, features: [String], category: String) {
        self.name = title
        self.description = description
        self.price = price
        self.imageURL = imageURL
        self.features = features
        self.category = category
    }
}

// MARK: - 客户问题
struct CustomerQuestion: Identifiable {
    let id = UUID()
    let question: String
    let category: String
    let priority: Int
}

// MARK: - 直播转录片段
struct TranscriptSegment {
    let id = UUID()
    let timestamp: TimeInterval
    let speaker: String // "主播" 或 "观众"
    let content: String
    let confidence: Double // 识别置信度
}

// MARK: - 直播数据统计
struct LiveStreamStats {
    let viewerCount: Int
    let peakViewers: Int
    let averageViewers: Int
    let likeCount: Int
    let shareCount: Int
    let commentCount: Int
    let giftCount: Int
    let newFollowers: Int
}

// MARK: - 直播表现分析
struct PerformanceAnalysis {
    let overallScore: Double
    let engagementScore: Double
    let contentQuality: Double
    let interactionLevel: Double
    let professionalLevel: Double
    let responseSpeed: Double
}

// MARK: - 直播总结
struct LiveStreamSummary {
    let duration: TimeInterval
    let startTime: Date
    let endTime: Date
    let productName: String
    let productUrl: String

    // 统计数据
    let stats: LiveStreamStats

    // 问题相关
    let totalQuestions: Int
    let answeredQuestions: Int
    let topQuestions: [String]

    // 转录内容
    let transcript: [TranscriptSegment]
    let fullTranscriptText: String

    // 分析结果
    let performanceAnalysis: PerformanceAnalysis
    let suggestions: [String]
    let highlights: [String] // 精彩片段
    let keyMoments: [(TimeInterval, String)] // 关键时刻

    // 综合评分
    let performanceScore: Double
}

// MARK: - 分析进度状态
enum AnalysisProgress {
    case idle
    case fetchingProduct
    case analyzingWithAI
    case generatingQuestions
    case completed

    var description: String {
        switch self {
        case .idle:
            return "准备开始"
        case .fetchingProduct:
            return "获取商品信息..."
        case .analyzingWithAI:
            return "AI智能分析中..."
        case .generatingQuestions:
            return "生成客户问题..."
        case .completed:
            return "分析完成"
        }
    }

    var progress: Double {
        switch self {
        case .idle:
            return 0.0
        case .fetchingProduct:
            return 0.25
        case .analyzingWithAI:
            return 0.65
        case .generatingQuestions:
            return 0.85
        case .completed:
            return 1.0
        }
    }
}

// MARK: - 分析日志管理器
class AnalysisLogManager: ObservableObject {
    @Published var logs: [AnalysisLog] = []
    @Published var isActive: Bool = false

    func addLog(_ message: String, type: AnalysisLogType = .info) {
        DispatchQueue.main.async {
            let log = AnalysisLog(message: message, type: type, timestamp: Date())
            self.logs.append(log)

            // 保持最多20条日志
            if self.logs.count > 20 {
                self.logs.removeFirst()
            }
        }
    }

    func start() {
        DispatchQueue.main.async {
            self.isActive = true
            self.logs.removeAll()
            self.addLog("🚀 开始分析任务", type: .success)
        }
    }

    func finish() {
        DispatchQueue.main.async {
            self.addLog("✅ 分析完成", type: .success)
            self.isActive = false
        }
    }

    func reset() {
        DispatchQueue.main.async {
            self.logs.removeAll()
            self.isActive = false
        }
    }
}

// MARK: - 分析日志数据模型
struct AnalysisLog: Identifiable {
    let id = UUID()
    let message: String
    let type: AnalysisLogType
    let timestamp: Date
}

enum AnalysisLogType {
    case info
    case success
    case warning
    case error

    var emoji: String {
        switch self {
        case .info: return "📝"
        case .success: return "✅"
        case .warning: return "⚠️"
        case .error: return "❌"
        }
    }

    var color: Color {
        switch self {
        case .info: return .blue
        case .success: return .green
        case .warning: return .orange
        case .error: return .red
        }
    }
}

// MARK: - 电商管理器
@MainActor
class ECommerceManager: ObservableObject {
    @Published var currentProduct: ProductInfo?
    @Published var customerQuestions: [CustomerQuestion] = []
    @Published var pendingQuestions: [CustomerQuestion] = [] // 待确认的问题
    @Published var isAnalyzing = false
    @Published var analysisProgress: AnalysisProgress = .idle
    @Published var errorMessage: String?
    @Published var liveStreamSummary: LiveStreamSummary?
    @Published var showQuestionConfirmation = false // 显示问题确认界面

    // AI模型管理器
    @Published var aiModelManager = AIModelManager()

    // 分析日志管理器
    @Published var analysisLog = AnalysisLogManager()

    // 缓存已分析的产品，避免重复分析
    private var productCache: [String: (ProductInfo, [CustomerQuestion])] = [:]

    // Task 管理 - 防止内存泄漏和卡死
    private var currentAnalysisTask: Task<Void, Never>?

    // 计算属性：是否使用了降级方案
    var isUsingFallback: Bool {
        return aiModelManager.isUsingFallback
    }

    // MARK: - 清理方法
    func cleanup() {
        currentAnalysisTask?.cancel()
        currentAnalysisTask = nil
        isAnalyzing = false
        analysisProgress = .idle
        errorMessage = nil
        analysisLog.reset()
    }

    // MARK: - 超时控制
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            // 添加主要操作
            group.addTask {
                try await operation()
            }

            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw TimeoutError()
            }

            // 返回第一个完成的结果
            guard let result = try await group.next() else {
                throw TimeoutError()
            }

            // 取消其他任务
            group.cancelAll()
            return result
        }
    }

    // MARK: - 测试AI模型
    func testAIModel(with product: ProductInfo) async {
        let testPrompt = AIPromptManager.testConnectionPrompt

        do {
            let response = try await aiModelManager.callAI(prompt: testPrompt)
            print("✅ AI模型测试成功: \(response)")

            DispatchQueue.main.async {
                self.errorMessage = nil
            }
        } catch {
            print("❌ AI模型测试失败: \(error)")

            DispatchQueue.main.async {
                self.errorMessage = "AI模型连接失败: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - 快捷商品分析
    func analyzeQuickProduct(_ productInfo: ProductInfo) async {
        DispatchQueue.main.async {
            self.isAnalyzing = true
            self.analysisProgress = .analyzingWithAI
            self.errorMessage = nil
            // 重置AI模型管理器状态
            self.aiModelManager.isUsingFallback = false
            // 开始日志记录
            self.analysisLog.start()
            self.analysisLog.addLog("📦 商品：\(productInfo.name)")
            self.analysisLog.addLog("🤖 AI模型：\(self.aiModelManager.currentModel.displayName)")
        }

        do {
            // 直接开始分批生成问题
            let questions = try await generateCustomerQuestions(for: productInfo)

            DispatchQueue.main.async {
                self.currentProduct = productInfo
                self.pendingQuestions = questions
                self.analysisProgress = .completed
                self.isAnalyzing = false
                self.analysisLog.finish()
                self.showQuestionConfirmation = true // 显示确认界面
            }

        } catch {
            let errorMsg = handleAnalysisError(error)
            DispatchQueue.main.async {
                self.errorMessage = errorMsg
                self.analysisProgress = .idle
                self.isAnalyzing = false
                self.analysisLog.addLog("❌ 分析失败：\(errorMsg)", type: .error)
                self.analysisLog.reset()
            }
        }
    }

    // MARK: - 后台快捷商品分析（新的交互逻辑）
    func analyzeQuickProductInBackground(_ productInfo: ProductInfo) async {
        // 取消之前的分析任务
        currentAnalysisTask?.cancel()

        // 创建新的分析任务
        currentAnalysisTask = Task { @MainActor in
            // 设置初始状态
            self.isAnalyzing = true
            self.analysisProgress = .analyzingWithAI
            self.errorMessage = nil
            self.currentProduct = productInfo // 立即设置当前商品
            // 重置AI模型管理器状态
            self.aiModelManager.isUsingFallback = false
            // 开始日志记录
            self.analysisLog.start()
            self.analysisLog.addLog("🚀 开始后台分析商品：\(productInfo.name)")
            self.analysisLog.addLog("🤖 AI模型：\(self.aiModelManager.currentModel.displayName)")

            do {
                // 检查任务是否被取消
                try Task.checkCancellation()

                // 分批生成问题，每生成一批就发送到弹幕
                let questions = try await generateCustomerQuestionsWithLiveUpdates(for: productInfo)

                // 再次检查任务是否被取消
                try Task.checkCancellation()

                // 更新完成状态
                self.customerQuestions = questions // 直接设置为确认的问题
                self.analysisProgress = .completed
                self.isAnalyzing = false
                self.analysisLog.finish()
                self.analysisLog.addLog("✅ 后台分析完成，共生成\(questions.count)个问题")

            } catch is CancellationError {
                // 任务被取消，清理状态
                self.isAnalyzing = false
                self.analysisProgress = .idle
                self.analysisLog.addLog("⚠️ 分析任务被取消", type: .warning)
                self.analysisLog.reset()
            } catch {
                let errorMsg = handleAnalysisError(error)
                self.errorMessage = errorMsg
                self.analysisProgress = .idle
                self.isAnalyzing = false
                self.analysisLog.addLog("❌ 后台分析失败：\(errorMsg)", type: .error)
                self.analysisLog.reset()
            }
        }

        // 等待任务完成
        await currentAnalysisTask?.value
    }

    // MARK: - 产品分析
    func analyzeProduct(from url: String) async {
        DispatchQueue.main.async {
            self.isAnalyzing = true
            self.analysisProgress = .fetchingProduct
            self.errorMessage = nil
            // 重置AI模型管理器状态
            self.aiModelManager.isUsingFallback = false
            // 开始日志记录
            self.analysisLog.start()
            self.analysisLog.addLog("🔗 商品链接：\(url)")
            self.analysisLog.addLog("🤖 AI模型：\(self.aiModelManager.currentModel.displayName)")
        }

        do {
            // 检查缓存
            if let cached = productCache[url] {
                DispatchQueue.main.async {
                    self.analysisLog.addLog("💾 使用缓存结果")
                    self.analysisProgress = .completed
                    self.currentProduct = cached.0
                    self.pendingQuestions = cached.1
                    self.isAnalyzing = false
                    self.analysisLog.finish()
                    self.showQuestionConfirmation = true // 显示确认界面
                }
                return
            }

            // 阶段1: 获取产品信息
            DispatchQueue.main.async {
                self.analysisLog.addLog("📦 获取商品信息...")
            }
            let productInfo = try await fetchProductInfo(from: url)

            DispatchQueue.main.async {
                self.analysisLog.addLog("✅ 商品信息获取成功：\(productInfo.name)", type: .success)
            }

            // 阶段2: 分批生成问题
            let questions = try await generateCustomerQuestions(for: productInfo)

            // 缓存结果
            productCache[url] = (productInfo, questions)

            DispatchQueue.main.async {
                self.currentProduct = productInfo
                self.pendingQuestions = questions
                self.analysisProgress = .completed
                self.isAnalyzing = false
                self.analysisLog.finish()
                self.showQuestionConfirmation = true // 显示确认界面
            }

        } catch {
            let errorMsg = handleAnalysisError(error)
            DispatchQueue.main.async {
                self.errorMessage = errorMsg
                self.analysisProgress = .idle
                self.isAnalyzing = false
                self.analysisLog.addLog("❌ 分析失败：\(errorMsg)", type: .error)
                self.analysisLog.reset()
            }
        }
    }

    // MARK: - 确认问题
    func confirmQuestions() {
        customerQuestions = pendingQuestions
        pendingQuestions = []
        showQuestionConfirmation = false
    }

    // 重载方法以支持传入确认的问题列表
    func confirmQuestions(_ confirmedQuestions: [CustomerQuestion]) {
        customerQuestions = confirmedQuestions
        pendingQuestions = []
        showQuestionConfirmation = false
    }

    // MARK: - 取消确认
    func cancelQuestionConfirmation() {
        pendingQuestions = []
        showQuestionConfirmation = false
    }

    // MARK: - 获取产品信息
    private func fetchProductInfo(from url: String) async throws -> ProductInfo {
        // 模拟网络请求延迟
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        // 这里应该实现真实的网页爬取逻辑
        // 目前返回模拟数据
        return ProductInfo(
            name: "智能手机",
            description: "最新款智能手机，配备先进的AI芯片和高清摄像头",
            price: "¥3999",
            imageURL: nil,
            features: ["AI芯片", "高清摄像头", "长续航", "快充技术"],
            category: "电子产品"
        )
    }

    // MARK: - 生成客户问题（分批请求）
    private func generateCustomerQuestions(for product: ProductInfo) async throws -> [CustomerQuestion] {
        var allQuestions: [CustomerQuestion] = []
        let categories = AIPromptManager.getBatchCategories()
        let totalBatches = categories.count

        print("🔄 开始分批生成问题，共\(totalBatches)批，每批5个问题")

        for (index, category) in categories.enumerated() {
            let batchNumber = index + 1

            DispatchQueue.main.async {
                self.analysisLog.addLog("📝 正在生成第\(batchNumber)/\(totalBatches)批问题：\(category)")
            }

            do {
                let prompt = AIPromptManager.getProductAnalysisPrompt(for: product, batch: batchNumber, category: category)

                DispatchQueue.main.async {
                    self.analysisLog.addLog("🔄 调用AI模型...")
                }

                let response = try await aiModelManager.callAI(prompt: prompt)
                let batchQuestions = parseBatchQuestionsFromResponse(response, category: category, batchIndex: index)

                allQuestions.append(contentsOf: batchQuestions)

                DispatchQueue.main.async {
                    self.analysisLog.addLog("✅ 第\(batchNumber)批完成，生成\(batchQuestions.count)个问题", type: .success)
                }

                // 批次间短暂延迟，避免请求过于频繁
                try await Task.sleep(nanoseconds: 300_000_000) // 0.3秒

            } catch {
                DispatchQueue.main.async {
                    self.analysisLog.addLog("⚠️ 第\(batchNumber)批生成失败，使用备用问题", type: .warning)
                }

                // 如果某批失败，生成该类别的备用问题，但不显示错误给用户
                let fallbackQuestions = generateFallbackQuestionsForCategory(category, batchIndex: index)
                allQuestions.append(contentsOf: fallbackQuestions)

                DispatchQueue.main.async {
                    self.analysisLog.addLog("✅ 第\(batchNumber)批备用问题生成完成 (\(fallbackQuestions.count)个)", type: .success)
                }

                try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒
            }
        }

        print("🎉 问题生成完成，共生成\(allQuestions.count)个问题")
        return allQuestions
    }

    // MARK: - 生成客户问题（带实时弹幕更新）
    private func generateCustomerQuestionsWithLiveUpdates(for product: ProductInfo) async throws -> [CustomerQuestion] {
        var allQuestions: [CustomerQuestion] = []
        let categories = AIPromptManager.getBatchCategories()
        let totalBatches = categories.count

        print("🔄 开始分批生成问题（实时弹幕），共\(totalBatches)批，每批5个问题")

        for (index, category) in categories.enumerated() {
            // 检查任务是否被取消
            try Task.checkCancellation()

            let batchNumber = index + 1

            await MainActor.run {
                self.analysisLog.addLog("📝 正在生成第\(batchNumber)/\(totalBatches)批问题：\(category)")
            }

            do {
                let prompt = AIPromptManager.getProductAnalysisPrompt(for: product, batch: batchNumber, category: category)

                await MainActor.run {
                    self.analysisLog.addLog("🔄 调用AI模型...")
                }

                // 添加超时控制，防止卡死
                let response = try await withTimeout(seconds: 30) { [self] in
                    try await self.aiModelManager.callAI(prompt: prompt)
                }

                let batchQuestions = parseBatchQuestionsFromResponse(response, category: category, batchIndex: index)
                allQuestions.append(contentsOf: batchQuestions)

                // 实时发送到弹幕
                await MainActor.run {
                    self.analysisLog.addLog("✅ 第\(batchNumber)批完成，生成\(batchQuestions.count)个问题", type: .success)

                    // 发送通知，让LiveStreamView添加问题到弹幕
                    for question in batchQuestions {
                        NotificationCenter.default.post(
                            name: .newQuestionGenerated,
                            object: question
                        )
                    }
                }

                // 批次间短暂延迟，避免请求过于频繁
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒，给弹幕更多时间显示

            } catch is CancellationError {
                // 任务被取消，直接抛出
                throw CancellationError()
            } catch {
                await MainActor.run {
                    self.analysisLog.addLog("⚠️ 第\(batchNumber)批生成失败，使用备用问题", type: .warning)
                }

                // 如果某批失败，生成该类别的备用问题
                let fallbackQuestions = generateFallbackQuestionsForCategory(category, batchIndex: index)
                allQuestions.append(contentsOf: fallbackQuestions)

                // 实时发送备用问题到弹幕
                await MainActor.run {
                    self.analysisLog.addLog("✅ 第\(batchNumber)批备用问题生成完成 (\(fallbackQuestions.count)个)", type: .success)

                    // 发送备用问题到弹幕
                    for question in fallbackQuestions {
                        NotificationCenter.default.post(
                            name: .newQuestionGenerated,
                            object: question
                        )
                    }
                }

                try await Task.sleep(nanoseconds: 300_000_000) // 0.3秒
            }
        }

        print("🎉 实时问题生成完成，共生成\(allQuestions.count)个问题")
        return allQuestions
    }

    // MARK: - 解析分批AI响应
    private func parseBatchQuestionsFromResponse(_ response: String, category: String, batchIndex: Int) -> [CustomerQuestion] {
        let lines = response.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        var questions: [CustomerQuestion] = []

        for (index, line) in lines.enumerated() {
            // 清理问题文本
            let cleanQuestion = line
                .replacingOccurrences(of: "^\\d+[.、]\\s*", with: "", options: .regularExpression)
                .replacingOccurrences(of: "^[•-]\\s*", with: "", options: .regularExpression)
                .trimmingCharacters(in: .whitespacesAndNewlines)

            if !cleanQuestion.isEmpty && cleanQuestion.count > 5 {
                // 根据批次索引设置优先级
                let priority = (batchIndex < 3) ? 1 : ((batchIndex < 7) ? 2 : 3)

                questions.append(CustomerQuestion(
                    question: cleanQuestion,
                    category: category,
                    priority: priority
                ))
            }

            // 每批最多5个问题
            if questions.count >= 5 {
                break
            }
        }

        return questions
    }

    // MARK: - 生成分类备用问题
    private func generateFallbackQuestionsForCategory(_ category: String, batchIndex: Int) -> [CustomerQuestion] {
        let fallbackQuestions: [String]

        switch category {
        case "产品功能和特性":
            fallbackQuestions = [
                "这个产品有什么主要功能？",
                "产品的核心特色是什么？",
                "使用方法复杂吗？",
                "有哪些实用的功能？",
                "产品技术先进吗？"
            ]
        case "质量和耐用性":
            fallbackQuestions = [
                "产品质量怎么样？",
                "材质好不好？",
                "能用多长时间？",
                "会不会容易坏？",
                "质量有保证吗？"
            ]
        case "使用体验和效果":
            fallbackQuestions = [
                "使用起来方便吗？",
                "效果明显吗？",
                "操作简单吗？",
                "用户体验好吗？",
                "实际效果如何？"
            ]
        case "性价比分析":
            fallbackQuestions = [
                "性价比高吗？",
                "价格合理吗？",
                "值得购买吗？",
                "比同类产品便宜吗？",
                "物超所值吗？"
            ]
        case "售后服务保障":
            fallbackQuestions = [
                "有售后服务吗？",
                "保修期多长？",
                "可以退换货吗？",
                "客服响应快吗？",
                "维修方便吗？"
            ]
        default:
            fallbackQuestions = [
                "这个产品怎么样？",
                "值得推荐吗？",
                "有什么优缺点？",
                "适合什么人用？",
                "购买建议是什么？"
            ]
        }

        let priority = (batchIndex < 3) ? 1 : ((batchIndex < 7) ? 2 : 3)

        return fallbackQuestions.map { question in
            CustomerQuestion(
                question: question,
                category: category,
                priority: priority
            )
        }
    }

    // MARK: - 生成备用问题
    private func generateFallbackQuestions(for product: ProductInfo?) -> [CustomerQuestion] {
        let categories = AIPromptManager.getBatchCategories()
        var allQuestions: [CustomerQuestion] = []

        for (index, category) in categories.enumerated() {
            let categoryQuestions = generateFallbackQuestionsForCategory(category, batchIndex: index)
            allQuestions.append(contentsOf: categoryQuestions)
        }

        return allQuestions
    }

    // MARK: - 错误处理
    private func handleAnalysisError(_ error: Error) -> String {
        print("🚨 分析错误: \(error)")

        if error is TimeoutError {
            return "请求超时，请检查网络连接后重试"
        }

        if error is CancellationError {
            return "操作已取消"
        }

        if let aiError = error as? AIModelError {
            switch aiError {
            case .networkError:
                return "网络连接失败，请检查网络设置"
            case .apiKeyInvalid:
                return "API密钥无效，请检查配置"
            case .rateLimitExceeded:
                return "请求过于频繁，请稍后再试"
            case .modelNotAvailable:
                return "AI模型暂时不可用，请稍后再试"
            case .invalidResponse:
                return "AI响应格式错误，请重试"
            case .quotaExceeded:
                return "API配额已用完，请稍后再试"
            }
        }

        return "分析失败，请重试"
    }

    // MARK: - 生成直播总结
    func generateLiveStreamSummary() async {
        DispatchQueue.main.async {
            self.isAnalyzing = true
        }

        do {
            let now = Date()
            let duration: TimeInterval = 1800 // 30分钟
            let startTime = now.addingTimeInterval(-duration)

            // 生成模拟转录内容
            let transcript = generateMockTranscript(duration: duration)
            let fullTranscriptText = transcript.map { "[\(formatTimestamp($0.timestamp))] \($0.speaker): \($0.content)" }.joined(separator: "\n")

            // 使用AI分析直播内容
            let transcriptSample = transcript.prefix(5).map { "[\(formatTimestamp($0.timestamp))] \($0.speaker): \($0.content)" }.joined(separator: "\n")
            let prompt = AIPromptManager.getLiveStreamAnalysisPrompt(
                productName: currentProduct?.name ?? "智能手机",
                duration: duration,
                questionCount: customerQuestions.count,
                topQuestionCategories: getTopQuestionCategories(),
                transcriptSample: transcriptSample
            )

            let response = try await aiModelManager.callAI(prompt: prompt)
            let summary = await parseAIAnalysisResponse(response, transcript: transcript, duration: duration, startTime: startTime, endTime: now)

            DispatchQueue.main.async {
                self.liveStreamSummary = summary
                self.isAnalyzing = false
            }

        } catch {
            // 如果AI调用失败，生成本地模拟报告
            await generateMockSummary()
        }
    }

    // MARK: - 生成模拟报告
    private func generateMockSummary() async {
        let now = Date()
        let duration: TimeInterval = 1800 // 30分钟
        let startTime = now.addingTimeInterval(-duration)

        // 生成模拟转录内容
        let transcript = generateMockTranscript(duration: duration)

        // 生成统计数据
        let stats = LiveStreamStats(
            viewerCount: Int.random(in: 1200...3500),
            peakViewers: Int.random(in: 3000...5000),
            averageViewers: Int.random(in: 1500...2800),
            likeCount: Int.random(in: 800...2000),
            shareCount: Int.random(in: 50...200),
            commentCount: Int.random(in: 300...800),
            giftCount: Int.random(in: 20...100),
            newFollowers: Int.random(in: 100...300)
        )

        // 生成表现分析
        let performanceAnalysis = PerformanceAnalysis(
            overallScore: Double.random(in: 75...95),
            engagementScore: Double.random(in: 70...90),
            contentQuality: Double.random(in: 80...95),
            interactionLevel: Double.random(in: 75...90),
            professionalLevel: Double.random(in: 70...85),
            responseSpeed: Double.random(in: 65...85)
        )

        let mockSummary = LiveStreamSummary(
            duration: duration,
            startTime: startTime,
            endTime: now,
            productName: currentProduct?.name ?? "智能手机",
            productUrl: "https://example.com/product",
            stats: stats,
            totalQuestions: customerQuestions.count,
            answeredQuestions: max(0, customerQuestions.count - Int.random(in: 0...3)),
            topQuestions: Array(customerQuestions.prefix(5).map { $0.question }),
            transcript: transcript,
            fullTranscriptText: transcript.map { $0.content }.joined(separator: "\n"),
            performanceAnalysis: performanceAnalysis,
            suggestions: generateRealisticSuggestions(),
            highlights: [
                "15:30 - 产品功能演示获得大量点赞",
                "22:45 - 回答电池续航问题引发热烈讨论",
                "28:10 - 限时优惠活动观众反响热烈"
            ],
            keyMoments: [
                (930, "开始产品演示"),
                (1365, "回答观众热门问题"),
                (1690, "发布限时优惠")
            ],
            performanceScore: performanceAnalysis.overallScore
        )

        DispatchQueue.main.async {
            self.liveStreamSummary = mockSummary
            self.isAnalyzing = false
        }
    }

    // MARK: - 生成模拟转录内容
    private func generateMockTranscript(duration: TimeInterval) -> [TranscriptSegment] {
        let hostComments = [
            "大家好，欢迎来到我的直播间！今天给大家带来一款非常棒的智能手机",
            "这款手机的摄像头真的很棒，我们来看看实际拍摄效果",
            "电池续航能力也是这款手机的一大亮点，正常使用可以坚持一整天",
            "现在我们来回答一下观众朋友们的问题",
            "这个价格确实很有竞争力，性价比非常高",
            "我们来看看这款手机的外观设计，手感真的很不错",
            "处理器性能很强劲，运行大型游戏也很流畅",
            "屏幕显示效果很清晰，色彩还原度很高",
            "今天的直播就到这里，感谢大家的观看和支持！"
        ]

        let audienceComments = [
            "这款手机多少钱？",
            "电池能用多久？",
            "摄像头像素多少？",
            "有什么颜色可以选择？",
            "支持5G吗？",
            "屏幕多大？",
            "内存有多大？",
            "什么时候发货？",
            "有优惠活动吗？",
            "质量怎么样？"
        ]

        var transcript: [TranscriptSegment] = []
        let segmentCount = Int.random(in: 25...40)

        for i in 0..<segmentCount {
            let timestamp = (duration / Double(segmentCount)) * Double(i)
            let isHost = i % 3 != 0 // 主播说话的比例更高

            let segment = TranscriptSegment(
                timestamp: timestamp,
                speaker: isHost ? "主播" : "观众",
                content: isHost ? hostComments.randomElement()! : audienceComments.randomElement()!,
                confidence: Double.random(in: 0.85...0.98)
            )

            transcript.append(segment)
        }

        return transcript.sorted { $0.timestamp < $1.timestamp }
    }

    // MARK: - 生成真实建议
    private func generateRealisticSuggestions() -> [String] {
        let suggestions = [
            "建议在产品演示时放慢语速，让观众更好地理解产品特点",
            "可以增加更多与观众的互动环节，提高直播间活跃度",
            "回答问题时可以更加详细，提供更多实用信息",
            "建议准备更多产品使用场景的演示，增强说服力",
            "可以适当增加一些优惠活动，刺激观众购买欲望",
            "建议改善直播间的灯光效果，让产品展示更清晰",
            "可以邀请一些忠实用户分享使用体验，增加可信度",
            "建议定期总结观众关心的问题，提前准备标准答案"
        ]

        return Array(suggestions.shuffled().prefix(Int.random(in: 4...6)))
    }

    // MARK: - 解析AI分析响应
    private func parseAIAnalysisResponse(_ response: String, transcript: [TranscriptSegment], duration: TimeInterval, startTime: Date, endTime: Date) async -> LiveStreamSummary {
        // 尝试解析JSON响应
        var overallScore: Double = 85.0
        var interactionLevel: Double = 82.0
        var contentQuality: Double = 88.0
        var professionalLevel: Double = 80.0
        var responseSpeed: Double = 75.0
        var suggestions: [String] = []
        var highlights: [String] = []

        // 简单的文本解析（如果JSON解析失败）
        let lines = response.components(separatedBy: .newlines)

        for line in lines {
            let cleanLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // 提取评分
            if cleanLine.contains("整体") || cleanLine.contains("overall") {
                if let score = extractScore(from: cleanLine) {
                    overallScore = score
                }
            } else if cleanLine.contains("互动") || cleanLine.contains("interaction") {
                if let score = extractScore(from: cleanLine) {
                    interactionLevel = score
                }
            } else if cleanLine.contains("内容") || cleanLine.contains("content") {
                if let score = extractScore(from: cleanLine) {
                    contentQuality = score
                }
            } else if cleanLine.contains("专业") || cleanLine.contains("professional") {
                if let score = extractScore(from: cleanLine) {
                    professionalLevel = score
                }
            } else if cleanLine.contains("响应") || cleanLine.contains("response") {
                if let score = extractScore(from: cleanLine) {
                    responseSpeed = score
                }
            }

            // 提取建议
            if (cleanLine.contains("建议") || cleanLine.contains("suggestion")) && cleanLine.count > 10 {
                suggestions.append(cleanLine)
            }

            // 提取精彩时刻
            if (cleanLine.contains("精彩") || cleanLine.contains("highlight")) && cleanLine.count > 10 {
                highlights.append(cleanLine)
            }
        }

        // 如果没有提取到足够的数据，使用默认值
        if suggestions.isEmpty {
            suggestions = generateRealisticSuggestions()
        }

        if highlights.isEmpty {
            highlights = [
                "15:30 - 产品功能演示获得大量点赞",
                "22:45 - 回答电池续航问题引发热烈讨论",
                "28:10 - 限时优惠活动观众反响热烈"
            ]
        }

        // 生成统计数据
        let stats = LiveStreamStats(
            viewerCount: Int.random(in: 1200...3500),
            peakViewers: Int.random(in: 3000...5000),
            averageViewers: Int.random(in: 1500...2800),
            likeCount: Int.random(in: 800...2000),
            shareCount: Int.random(in: 50...200),
            commentCount: Int.random(in: 300...800),
            giftCount: Int.random(in: 20...100),
            newFollowers: Int.random(in: 100...300)
        )

        // 生成表现分析
        let performanceAnalysis = PerformanceAnalysis(
            overallScore: overallScore,
            engagementScore: Double.random(in: 70...90),
            contentQuality: contentQuality,
            interactionLevel: interactionLevel,
            professionalLevel: professionalLevel,
            responseSpeed: responseSpeed
        )

        return LiveStreamSummary(
            duration: duration,
            startTime: startTime,
            endTime: endTime,
            productName: currentProduct?.name ?? "智能手机",
            productUrl: "https://example.com/product",
            stats: stats,
            totalQuestions: customerQuestions.count,
            answeredQuestions: max(0, customerQuestions.count - Int.random(in: 0...3)),
            topQuestions: Array(customerQuestions.prefix(5).map { $0.question }),
            transcript: transcript,
            fullTranscriptText: transcript.map { "[\(formatTimestamp($0.timestamp))] \($0.speaker): \($0.content)" }.joined(separator: "\n"),
            performanceAnalysis: performanceAnalysis,
            suggestions: Array(suggestions.prefix(5)),
            highlights: Array(highlights.prefix(3)),
            keyMoments: [
                (930, "开始产品演示"),
                (1365, "回答观众热门问题"),
                (1690, "发布限时优惠")
            ],
            performanceScore: overallScore
        )
    }

    // MARK: - 提取评分
    private func extractScore(from text: String) -> Double? {
        let numbers = text.components(separatedBy: CharacterSet.decimalDigits.inverted)
            .compactMap { Double($0) }
            .filter { $0 >= 0 && $0 <= 100 }
        return numbers.first
    }

    // MARK: - 格式化时间戳
    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let minutes = Int(timestamp) / 60
        let seconds = Int(timestamp) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - 获取热门问题类别
    private func getTopQuestionCategories() -> String {
        let categoryCount = Dictionary(grouping: customerQuestions, by: { $0.category })
            .mapValues { $0.count }
            .sorted { $0.value > $1.value }

        return categoryCount.prefix(3).map { $0.key }.joined(separator: "、")
    }



    // MARK: - 重置状态
    func reset() {
        currentProduct = nil
        customerQuestions = []
        pendingQuestions = []
        isAnalyzing = false
        analysisProgress = .idle
        errorMessage = nil
        liveStreamSummary = nil
        showQuestionConfirmation = false
        analysisLog.reset()
    }
}
