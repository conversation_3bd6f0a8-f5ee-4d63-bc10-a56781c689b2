//
//  AIModelManager.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import Foundation
import SwiftUI

// MARK: - AI模型错误类型
enum AIModelError: Error {
    case networkError
    case apiKeyInvalid
    case rateLimitExceeded
    case modelNotAvailable
    case invalidResponse
    case quotaExceeded
}

// MARK: - AI模型管理器
class AIModelManager: ObservableObject {
    @Published var currentModel: AIModel = .doubaoSeed // 默认使用豆包模型
    @Published var isProcessing = false
    @Published var errorMessage: String?
    @Published var isUsingFallback = false

    // API配置
    private let doubaoAPIKey = "e9e21053-6373-4315-b22f-196e5ab9fb04"
    
    // 重试配置
    private let maxRetries = 3
    private var hasTriedFallback = false
    
    // MARK: - 模型切换
    func switchToModel(_ model: AIModel) {
        currentModel = model
        hasTriedFallback = false
        print("🔄 切换到模型: \(model.displayName)")
    }

    // MARK: - 网络连接测试
    func testNetworkConnection() async -> Bool {
        do {
            let testURL = URL(string: "https://www.google.com")!
            let (_, response) = try await URLSession.shared.data(from: testURL)
            if let httpResponse = response as? HTTPURLResponse {
                print("🌐 网络连接测试: HTTP \(httpResponse.statusCode)")
                return httpResponse.statusCode == 200
            }
        } catch {
            print("❌ 网络连接测试失败: \(error)")
        }
        return false
    }

    // MARK: - 豆包API连通性测试
    func testDoubaoConnection() async -> Bool {
        do {
            let testURL = URL(string: "https://ark.cn-beijing.volces.com")!
            let request = URLRequest(url: testURL, timeoutInterval: 10)
            let (_, response) = try await URLSession.shared.data(for: request)
            if let httpResponse = response as? HTTPURLResponse {
                print("🌐 豆包API连通性测试: HTTP \(httpResponse.statusCode)")
                return httpResponse.statusCode < 500 // 接受4xx和2xx状态码
            }
        } catch {
            print("❌ 豆包API连通性测试失败: \(error)")
        }
        return false
    }
    
    // MARK: - 统一API调用接口
    func callAI(prompt: String, retryCount: Int = 0) async throws -> String {
        DispatchQueue.main.async {
            self.isProcessing = true
            self.errorMessage = nil
        }

        defer {
            DispatchQueue.main.async {
                self.isProcessing = false
            }
        }

        do {
            let response: String

            switch currentModel.provider {
            case .doubao:
                response = try await callDoubaoAPI(prompt: prompt)
            }

            DispatchQueue.main.async {
                self.isUsingFallback = false
            }

            return response

        } catch {
            print("❌ AI API调用失败: \(error.localizedDescription)")

            // 智能重试逻辑 - 根据错误类型决定是否重试
            let shouldRetry = shouldRetryForError(error)

            if shouldRetry && retryCount < 2 {  // 最多重试2次
                let retryDelay = getRetryDelay(for: error, retryCount: retryCount)
                print("⏳ 等待 \(retryDelay) 秒后重试... (第\(retryCount + 1)次重试)")

                try await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                return try await callAI(prompt: prompt, retryCount: retryCount + 1)
            }

            // 重试失败或不应重试，使用本地降级方案
            print("⚠️ 豆包API调用失败，使用本地降级方案")
            DispatchQueue.main.async {
                self.isUsingFallback = true
                self.errorMessage = self.getErrorMessage(for: error)
            }

            return generateFallbackResponse(for: prompt)
        }
    }
    
    // MARK: - 豆包 API调用
    private func callDoubaoAPI(prompt: String) async throws -> String {
        // 使用经过验证的API端点
        let apiURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        print("🔗 豆包 API URL: \(apiURL)")
        print("🔑 豆包 API Key: \(doubaoAPIKey.prefix(10))...")
        print("📝 请求内容长度: \(prompt.count) 字符")

        guard let url = URL(string: apiURL) else {
            throw NSError(domain: "InvalidURL", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的豆包 API地址"])
        }

        // 根据提示词复杂度动态调整参数
        let isComplexAnalysis = prompt.contains("直播带货表现") ||
                               prompt.contains("专业评估") ||
                               prompt.contains("生成50个客户可能关心的问题") ||
                               prompt.contains("商品分析") ||
                               prompt.contains("产品分析") ||
                               prompt.count > 1000
        let maxTokens = isComplexAnalysis ? 1500 : 500  // 复杂分析需要更多token
        let temperature = isComplexAnalysis ? 0.5 : 0.3  // 复杂分析需要更多创造性

        print("🧠 检测到\(isComplexAnalysis ? "复杂" : "简单")分析任务，调整参数: tokens=\(maxTokens), temp=\(temperature)")

        // 根据官方文档构建正确的请求体
        let requestBody: [String: Any] = [
            "model": currentModel.rawValue,
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": maxTokens,
            "temperature": temperature,
            "stream": false
        ]

        print("📤 豆包 请求模型: \(currentModel.rawValue)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(doubaoAPIKey)", forHTTPHeaderField: "Authorization")

        // 根据官方文档添加必要的请求头
        request.setValue("wenla-ios-app/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
            print("📦 请求体大小: \(request.httpBody?.count ?? 0) bytes")
        } catch {
            print("❌ 豆包 请求序列化失败: \(error)")
            throw NSError(domain: "SerializationError", code: 0, userInfo: [NSLocalizedDescriptionKey: "请求序列化失败: \(error.localizedDescription)"])
        }

        // 根据任务复杂度动态调整超时时间
        let timeoutInterval: TimeInterval = isComplexAnalysis ? 60 : 30  // 复杂分析给60秒
        request.timeoutInterval = timeoutInterval
        print("⏰ 设置超时时间: \(timeoutInterval)秒")

        // 使用优化的URLSession配置
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = timeoutInterval
        config.timeoutIntervalForResource = timeoutInterval * 2
        config.waitsForConnectivity = false
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        let session = URLSession(configuration: config)

        // 移除网络连接测试，直接进行API调用

        do {
            print("🌐 开始豆包API请求...")
            let startTime = Date()
            let (data, response) = try await session.data(for: request)
            let responseTime = Date().timeIntervalSince(startTime)
            print("⏱️ 豆包API响应时间: \(String(format: "%.2f", responseTime))秒")

            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 豆包 无效响应类型")
                throw NSError(domain: "InvalidResponse", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的响应"])
            }

            print("📡 豆包 HTTP状态码: \(httpResponse.statusCode)")
            print("📋 豆包 响应头: \(httpResponse.allHeaderFields)")

            // 处理不同的HTTP状态码
            switch httpResponse.statusCode {
            case 200:
                break // 成功，继续处理
            case 400:
                let errorMessage = String(data: data, encoding: .utf8) ?? "请求参数错误"
                print("❌ 豆包 API请求参数错误: \(errorMessage)")
                throw NSError(domain: "BadRequest", code: 400, userInfo: [NSLocalizedDescriptionKey: "请求参数错误: \(errorMessage)"])
            case 401:
                let errorMessage = String(data: data, encoding: .utf8) ?? "API Key无效"
                print("❌ 豆包 API认证失败: \(errorMessage)")
                throw NSError(domain: "Unauthorized", code: 401, userInfo: [NSLocalizedDescriptionKey: "API Key无效，请检查配置"])
            case 429:
                let errorMessage = String(data: data, encoding: .utf8) ?? "请求频率过高"
                print("❌ 豆包 API请求频率限制: \(errorMessage)")
                throw NSError(domain: "RateLimited", code: 429, userInfo: [NSLocalizedDescriptionKey: "请求频率过高，请稍后重试"])
            case 500...599:
                let errorMessage = String(data: data, encoding: .utf8) ?? "服务器内部错误"
                print("❌ 豆包 API服务器错误: \(errorMessage)")
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "服务器错误，请稍后重试"])
            default:
                let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
                print("❌ 豆包 API未知错误: \(errorMessage)")
                throw NSError(domain: "APIError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API错误 (\(httpResponse.statusCode)): \(errorMessage)"])
            }

            // 解析响应数据
            let responseString = String(data: data, encoding: .utf8) ?? "无法解码响应"
            print("📥 豆包 原始响应: \(responseString.prefix(200))...")

            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                print("❌ 豆包 JSON解析失败，响应内容: \(responseString)")
                throw NSError(domain: "ParseError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析豆包 API响应"])
            }

            print("📥 豆包 响应JSON keys: \(json.keys)")

            // 检查是否有错误信息
            if let error = json["error"] as? [String: Any],
               let errorMessage = error["message"] as? String {
                print("❌ 豆包 API返回错误: \(errorMessage)")
                throw NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "豆包 API错误: \(errorMessage)"])
            }

            // 解析成功响应
            guard let choices = json["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                print("❌ 豆包 响应格式错误，JSON: \(json)")
                throw NSError(domain: "ParseError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析豆包 API响应格式"])
            }

            print("✅ 豆包 API调用成功，响应长度: \(content.count)")
            return content.trimmingCharacters(in: .whitespacesAndNewlines)

        } catch {
            print("❌ 豆包 网络请求失败: \(error)")

            // 详细的错误处理
            if let urlError = error as? URLError {
                switch urlError.code {
                case .timedOut:
                    print("⏰ 豆包API请求超时")
                    throw NSError(domain: "TimeoutError", code: -1001, userInfo: [NSLocalizedDescriptionKey: "豆包API请求超时，请检查网络连接或稍后重试"])
                case .notConnectedToInternet:
                    print("🌐 网络未连接")
                    throw NSError(domain: "NetworkError", code: -1009, userInfo: [NSLocalizedDescriptionKey: "网络未连接，请检查网络设置"])
                case .networkConnectionLost:
                    print("📡 网络连接丢失")
                    throw NSError(domain: "NetworkError", code: -1005, userInfo: [NSLocalizedDescriptionKey: "网络连接丢失，请重试"])
                case .cannotFindHost:
                    print("🔍 无法找到服务器")
                    throw NSError(domain: "NetworkError", code: -1003, userInfo: [NSLocalizedDescriptionKey: "无法连接到豆包服务器，请检查网络设置"])
                case .cannotConnectToHost:
                    print("🔗 无法连接到服务器")
                    throw NSError(domain: "NetworkError", code: -1004, userInfo: [NSLocalizedDescriptionKey: "无法连接到豆包服务器，请稍后重试"])
                default:
                    print("🔗 其他网络错误: \(urlError.localizedDescription)")
                    throw NSError(domain: "NetworkError", code: urlError.code.rawValue, userInfo: [NSLocalizedDescriptionKey: "网络错误: \(urlError.localizedDescription)"])
                }
            }

            throw error
        }
    }
    


    // MARK: - 网络诊断方法

    func testNetworkConnectivity() async -> Bool {
        do {
            let testURL = URL(string: "https://www.baidu.com")!
            let (_, response) = try await URLSession.shared.data(from: testURL)
            if let httpResponse = response as? HTTPURLResponse {
                print("✅ 网络连接测试成功，状态码: \(httpResponse.statusCode)")
                return httpResponse.statusCode == 200
            }
        } catch {
            print("❌ 网络连接测试失败: \(error.localizedDescription)")
        }
        return false
    }

    func testDoubaoEndpoint() async -> String? {
        let endpoints = [
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            "https://ark.cn-beijing.volces.com/api/v1/chat/completions"
        ]

        for endpoint in endpoints {
            guard let url = URL(string: endpoint) else { continue }

            do {
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                request.setValue("Bearer test", forHTTPHeaderField: "Authorization")
                request.timeoutInterval = 10

                let testBody: [String: Any] = [
                    "model": "test",
                    "messages": [["role": "user", "content": "test"]],
                    "max_tokens": 1
                ]
                request.httpBody = try JSONSerialization.data(withJSONObject: testBody)

                let (_, response) = try await URLSession.shared.data(for: request)
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔍 端点 \(endpoint) 响应状态码: \(httpResponse.statusCode)")
                    // 即使是401（未授权）也说明端点是可达的
                    if httpResponse.statusCode == 401 || httpResponse.statusCode == 400 {
                        print("✅ 端点可达: \(endpoint)")
                        return endpoint
                    }
                }
            } catch {
                print("❌ 端点测试失败 \(endpoint): \(error.localizedDescription)")
            }
        }

        return nil
    }

    // MARK: - 辅助方法

    // 判断是否应该重试
    private func shouldRetryForError(_ error: Error) -> Bool {
        if let nsError = error as NSError? {
            switch nsError.domain {
            case "TimeoutError", "NetworkError":
                return true
            case "RateLimited":
                return true  // 频率限制可以重试
            case "ServerError":
                return true  // 服务器错误可以重试
            case "BadRequest", "Unauthorized":
                return false // 请求错误和认证错误不应重试
            default:
                return true  // 其他错误默认重试
            }
        }

        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .cannotConnectToHost:
                return true
            case .notConnectedToInternet, .cannotFindHost:
                return false
            default:
                return true
            }
        }

        return true
    }

    // 获取重试延迟时间
    private func getRetryDelay(for error: Error, retryCount: Int) -> Double {
        if let nsError = error as NSError?, nsError.domain == "RateLimited" {
            // 频率限制错误使用更长的延迟
            return Double(retryCount + 1) * 3.0  // 3秒, 6秒
        }

        let baseDelay = 1.0 // 基础延迟1秒
        let exponentialDelay = baseDelay * pow(1.5, Double(retryCount)) // 指数退避
        return min(exponentialDelay, 5.0) // 最大延迟5秒
    }

    // 获取用户友好的错误信息
    private func getErrorMessage(for error: Error) -> String {
        if let nsError = error as NSError? {
            switch nsError.domain {
            case "TimeoutError":
                return "豆包AI响应超时，已启用本地分析模式"
            case "NetworkError":
                return "网络连接异常，已启用本地分析模式"
            case "RateLimited":
                return "请求频率过高，已启用本地分析模式"
            case "Unauthorized":
                return "API密钥无效，已启用本地分析模式"
            case "ServerError":
                return "豆包AI服务暂时不可用，已启用本地分析模式"
            default:
                return "豆包AI暂时无法连接，已启用本地分析模式"
            }
        }

        return "豆包AI暂时无法连接，已启用本地分析模式"
    }
    
    private func generateFallbackResponse(for prompt: String) -> String {
        // 智能本地降级响应
        if prompt.contains("生成") && prompt.contains("问题") {
            return """
            [
              {
                "question": "这个产品的质量怎么样？值得购买吗？",
                "customerName": "品质达人"
              },
              {
                "question": "现在买有优惠吗？价格能再便宜点吗？",
                "customerName": "省钱专家"
              },
              {
                "question": "发货速度快吗？什么时候能收到？",
                "customerName": "急需用户"
              },
              {
                "question": "用起来舒服吗？手感怎么样？",
                "customerName": "体验达人"
              },
              {
                "question": "有什么颜色可以选择？",
                "customerName": "时尚小姐"
              },
              {
                "question": "售后服务怎么样？有保修吗？",
                "customerName": "服务关注者"
              }
            ]
            """
        } else if prompt.contains("分析直播表现") || prompt.contains("直播带货分析") {
            // 直播分析的降级响应
            return """
            评分：85分

            优化建议：
            1. 增加产品展示细节，让观众更直观了解产品特点
            2. 回答问题时语速适中，确保观众能听清楚
            3. 增加互动环节，如抽奖或限时优惠活动
            """
        }

        return "AI服务暂时不可用，已生成本地分析结果。"
    }
}
