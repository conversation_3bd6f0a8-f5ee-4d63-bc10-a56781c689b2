//
//  CameraManager.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import AVFoundation
import SwiftUI

class CameraManager: NSObject, ObservableObject {
    @Published var isAuthorized = false
    @Published var isCameraActive = false
    @Published var previewLayer: AVCaptureVideoPreviewLayer?
    @Published var errorMessage: String?
    @Published var currentCameraPosition: AVCaptureDevice.Position = .front // 默认前置摄像头

    private var captureSession: AVCaptureSession?
    private var videoDeviceInput: AVCaptureDeviceInput?
    
    override init() {
        super.init()
        checkCameraPermission()
    }
    
    func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            print("📷 摄像头权限已授权")
            isAuthorized = true
        case .notDetermined:
            print("📷 摄像头权限未确定，请求权限中...")
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    self?.isAuthorized = granted
                    print("📷 摄像头权限请求结果: \(granted)")
                    if granted {
                        // 权限获得后自动启动摄像头
                        self?.startCameraAfterPermission()
                    }
                }
            }
        case .denied, .restricted:
            print("📷 摄像头权限被拒绝或受限")
            isAuthorized = false
        @unknown default:
            print("📷 摄像头权限状态未知")
            isAuthorized = false
        }
    }

    private func startCameraAfterPermission() {
        print("📷 权限获得后启动摄像头")

        #if targetEnvironment(simulator)
        print("📱 模拟器环境：权限获得后启动摄像头")
        DispatchQueue.main.async { [weak self] in
            self?.isCameraActive = true
            print("📱 模拟器：前置摄像头已启动，状态: \(self?.isCameraActive ?? false)")
        }
        #else
        guard !isCameraActive else {
            print("📷 摄像头已经在运行中")
            return
        }

        print("📷 启动前置摄像头...")
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.setupCaptureSession()
        }
        #endif
    }
    
    func startCamera() {
        print("📷 开始启动摄像头...")
        print("📷 当前权限状态: \(isAuthorized)")
        print("📷 当前摄像头状态: \(isCameraActive)")

        // 清除之前的错误信息
        DispatchQueue.main.async { [weak self] in
            self?.errorMessage = nil
        }

        // 检查当前权限状态
        let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
        print("📷 系统权限状态: \(authStatus.rawValue)")

        switch authStatus {
        case .authorized:
            // 权限已授权，直接启动
            startCameraDirectly()
        case .notDetermined:
            // 权限未确定，请求权限
            print("� 权限未确定，请求权限...")
            checkCameraPermission()
        case .denied, .restricted:
            print("📷 摄像头权限被拒绝或受限，无法启动")
            DispatchQueue.main.async { [weak self] in
                self?.errorMessage = "摄像头权限被拒绝，请在设置中允许访问摄像头"
            }
        @unknown default:
            print("� 未知权限状态")
            checkCameraPermission()
        }
    }

    private func startCameraDirectly() {
        print("📷 直接启动摄像头")

        // 在模拟器中，我们只是模拟摄像头启动
        #if targetEnvironment(simulator)
        print("� 模拟器环境：直接启动摄像头")
        DispatchQueue.main.async { [weak self] in
            self?.isCameraActive = true
            print("📱 模拟器：前置摄像头已启动，状态: \(self?.isCameraActive ?? false)")
        }
        #else
        guard !isCameraActive else {
            print("📷 摄像头已经在运行中")
            return
        }

        print("📷 启动\(currentCameraPosition == .front ? "前置" : "后置")摄像头...")
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.setupCaptureSession()
        }
        #endif
    }
    
    func stopCamera() {
        guard isCameraActive else { return }

        #if targetEnvironment(simulator)
        DispatchQueue.main.async { [weak self] in
            self?.isCameraActive = false
            print("📱 模拟器：摄像头已停止")
        }
        #else
        print("📷 停止摄像头...")

        // 在后台线程停止会话
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession?.stopRunning()

            DispatchQueue.main.async {
                self?.isCameraActive = false
                self?.previewLayer = nil
                print("📷 摄像头已停止")
            }
        }
        #endif
    }
    
    func switchCamera() {
        #if targetEnvironment(simulator)
        // 在模拟器中只是切换状态
        let oldPosition = currentCameraPosition
        currentCameraPosition = currentCameraPosition == .front ? .back : .front
        print("📱 模拟器：摄像头切换 \(oldPosition == .front ? "前置" : "后置") -> \(currentCameraPosition == .front ? "前置" : "后置")")
        #else
        guard let captureSession = captureSession,
              let currentInput = videoDeviceInput else {
            print("📷 切换摄像头失败：会话或输入设备不存在")
            return
        }

        let oldPosition = currentCameraPosition
        captureSession.beginConfiguration()
        captureSession.removeInput(currentInput)

        // 切换摄像头位置
        currentCameraPosition = currentCameraPosition == .front ? .back : .front

        if let newDevice = AVCaptureDevice.default(.builtInWideAngleCamera,
                                                  for: .video,
                                                  position: currentCameraPosition),
           let newInput = try? AVCaptureDeviceInput(device: newDevice) {

            if captureSession.canAddInput(newInput) {
                captureSession.addInput(newInput)
                videoDeviceInput = newInput
                print("📷 摄像头切换成功：\(oldPosition == .front ? "前置" : "后置") -> \(currentCameraPosition == .front ? "前置" : "后置")")
            } else {
                // 如果无法添加新输入，恢复原来的输入
                captureSession.addInput(currentInput)
                currentCameraPosition = oldPosition
                print("📷 切换摄像头失败：无法添加新输入，已恢复原设置")
            }
        } else {
            // 如果无法创建新输入，恢复原来的输入
            captureSession.addInput(currentInput)
            currentCameraPosition = oldPosition
            print("📷 切换摄像头失败：无法创建新设备输入，已恢复原设置")
        }

        captureSession.commitConfiguration()
        #endif
    }
    
    private func setupCaptureSession() {
        print("📷 设置摄像头会话...")

        let session = AVCaptureSession()
        session.beginConfiguration()

        // 设置会话预设
        if session.canSetSessionPreset(.high) {
            session.sessionPreset = .high
            print("📷 会话预设：高质量")
        } else {
            print("📷 警告：无法设置高质量预设")
        }

        // 添加视频输入 - 默认使用前置摄像头
        guard let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera,
                                                        for: .video,
                                                        position: currentCameraPosition) else {
            print("📷 错误：无法获取\(currentCameraPosition == .front ? "前置" : "后置")摄像头设备")
            DispatchQueue.main.async { [weak self] in
                self?.errorMessage = "无法访问\(self?.currentCameraPosition == .front ? "前置" : "后置")摄像头"
            }
            return
        }

        guard let videoInput = try? AVCaptureDeviceInput(device: videoDevice) else {
            print("📷 错误：无法创建摄像头输入")
            DispatchQueue.main.async { [weak self] in
                self?.errorMessage = "无法创建摄像头输入"
            }
            return
        }

        if session.canAddInput(videoInput) {
            session.addInput(videoInput)
            videoDeviceInput = videoInput
            print("📷 成功添加\(currentCameraPosition == .front ? "前置" : "后置")摄像头输入")
        } else {
            print("📷 错误：无法添加摄像头输入到会话")
            DispatchQueue.main.async { [weak self] in
                self?.errorMessage = "无法添加摄像头输入"
            }
            return
        }

        session.commitConfiguration()

        // 创建预览层
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        previewLayer.videoGravity = .resizeAspectFill

        DispatchQueue.main.async { [weak self] in
            self?.captureSession = session
            self?.previewLayer = previewLayer
            print("📷 摄像头会话设置完成，开始运行")

            // 启动会话
            DispatchQueue.global(qos: .userInitiated).async {
                session.startRunning()
                DispatchQueue.main.async {
                    self?.isCameraActive = true
                    print("📷 摄像头已启动，状态: \(self?.isCameraActive ?? false)")
                }
            }
        }
    }

    // 获取当前摄像头位置的描述
    var currentCameraDescription: String {
        return currentCameraPosition == .front ? "前置摄像头" : "后置摄像头"
    }
}
