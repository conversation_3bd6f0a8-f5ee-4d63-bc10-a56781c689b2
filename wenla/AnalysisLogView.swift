//
//  AnalysisLogView.swift
//  wenla
//
//  Created by AI Assistant on 2025-07-12.
//

import SwiftUI

struct AnalysisLogView: View {
    @ObservedObject var logManager: AnalysisLogManager
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("分析日志")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                if logManager.isActive {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: logManager.isActive)
                        
                        Text("运行中")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
            }
            
            // 日志列表
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        ForEach(logManager.logs) { log in
                            LogRowView(log: log)
                                .id(log.id)
                        }
                    }
                    .padding(.vertical, 8)
                }
                .frame(maxHeight: 300)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.3))
                )
                .onChange(of: logManager.logs.count) {
                    // 自动滚动到最新日志
                    if let lastLog = logManager.logs.last {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(lastLog.id, anchor: .bottom)
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding(.horizontal, 20)
    }
}

struct LogRowView: View {
    let log: AnalysisLog
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // 时间戳
            Text(formatTime(log.timestamp))
                .font(.system(size: 10, design: .monospaced))
                .foregroundColor(.gray)
                .frame(width: 50, alignment: .leading)
            
            // 日志内容
            Text(log.message)
                .font(.system(size: 13))
                .foregroundColor(log.type.color)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(log.type.color.opacity(0.1))
        )
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
}

// 预览
struct AnalysisLogView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.gray.opacity(0.1)
                .ignoresSafeArea()
            
            AnalysisLogView(logManager: {
                let manager = AnalysisLogManager()
                manager.addLog("🚀 开始分析任务", type: .success)
                manager.addLog("📦 商品：iPhone 15 Pro")
                manager.addLog("🤖 AI模型：豆包")
                manager.addLog("📝 正在生成第1/10批问题：产品功能和特性")
                manager.addLog("🔄 调用AI模型...")
                manager.addLog("✅ 第1批完成，生成5个问题", type: .success)
                manager.addLog("⚠️ 第2批生成失败，使用备用问题", type: .warning)
                return manager
            }())
        }
    }
}
