//
//  ImagePicker.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/8.
//

import SwiftUI
import PhotosUI

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImageData: Data?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 1
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.presentationMode.wrappedValue.dismiss()

            guard let provider = results.first?.itemProvider else { return }

            if provider.canLoadObject(ofClass: UIImage.self) {
                provider.loadObject(ofClass: UIImage.self) { image, error in
                    DispatchQueue.main.async {
                        if let error = error {
                            print("❌ 图片加载失败: \(error.localizedDescription)")
                            return
                        }

                        guard let uiImage = image as? UIImage else {
                            print("❌ 图片转换失败")
                            return
                        }

                        // 更严格的图片压缩，专门为头像优化
                        let compressedImage = self.compressImageForAvatar(uiImage)

                        // 验证压缩后的图片数据
                        if let imageData = compressedImage.jpegData(compressionQuality: 0.7) {
                            // 再次验证数据是否可以创建UIImage
                            if UIImage(data: imageData) != nil {
                                self.parent.selectedImageData = imageData
                                print("✅ 头像图片处理成功，大小: \(imageData.count / 1024)KB")
                            } else {
                                print("❌ 压缩后的图片数据无效")
                            }
                        } else {
                            print("❌ 图片压缩失败")
                        }
                    }
                }
            }
        }
        
        // 专门为头像优化的压缩方法
        private func compressImageForAvatar(_ image: UIImage) -> UIImage {
            // 头像的最佳尺寸：200x200 像素，足够清晰且文件小
            let targetSize = CGSize(width: 200, height: 200)
            let maxSizeKB = 100 // 头像最大100KB

            // 首先调整尺寸到合适大小
            let resizedImage = resizeImage(image, to: targetSize)

            // 然后压缩质量
            return compressImageQuality(resizedImage, maxSizeKB: maxSizeKB)
        }

        // 调整图片尺寸
        private func resizeImage(_ image: UIImage, to targetSize: CGSize) -> UIImage {
            let size = image.size

            let widthRatio  = targetSize.width  / size.width
            let heightRatio = targetSize.height / size.height

            // 使用较小的比例以保持宽高比
            let ratio = min(widthRatio, heightRatio)
            let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)

            // 使用更高效的图片渲染方法
            let renderer = UIGraphicsImageRenderer(size: newSize)
            let resizedImage = renderer.image { _ in
                image.draw(in: CGRect(origin: .zero, size: newSize))
            }

            return resizedImage
        }

        // 压缩图片质量
        private func compressImageQuality(_ image: UIImage, maxSizeKB: Int) -> UIImage {
            let maxBytes = maxSizeKB * 1024
            var compression: CGFloat = 0.9 // 从较高质量开始
            var imageData = image.jpegData(compressionQuality: compression)

            // 逐步降低质量直到满足大小要求
            while let data = imageData, data.count > maxBytes && compression > 0.3 {
                compression -= 0.1
                imageData = image.jpegData(compressionQuality: compression)
            }

            // 如果压缩后仍然太大，进一步缩小尺寸
            if let data = imageData, data.count > maxBytes {
                let ratio: CGFloat = 0.8 // 缩小到80%
                let newSize = CGSize(width: image.size.width * ratio, height: image.size.height * ratio)

                let renderer = UIGraphicsImageRenderer(size: newSize)
                let smallerImage = renderer.image { _ in
                    image.draw(in: CGRect(origin: .zero, size: newSize))
                }

                // 递归压缩直到满足要求
                return compressImageQuality(smallerImage, maxSizeKB: maxSizeKB)
            }

            return image
        }
    }
}
