//
//  LiveStreamManager.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import Foundation
import SwiftUI

class LiveStreamManager: ObservableObject {
    @Published var isLive = false
    @Published var stats = LiveStats()
    @Published var chatMessages: [ChatMessage] = []

    private var startTime: Date?
    private var statsTimer: Timer?
    private var chatTimer: Timer?
    private var viewerCountTimer: Timer? // 观看人数跳动定时器

    // 引用ECommerceManager来获取AI生成的问题
    weak var ecommerceManager: ECommerceManager?
    
    // MARK: - 直播控制
    func startLiveStream() {
        isLive = true
        startTime = Date()

        // 初始化数据
        stats.viewerCount = Int.random(in: 80000...100000)  // 在线人数8万-10万
        stats.likeCount = Int.random(in: 100...1000)     // 初始点赞数100-1000

        startStatsTimer()
        startChatTimer()
        startViewerCountTimer() // 启动观看人数跳动定时器
    }

    func stopLiveStream() {
        isLive = false
        stopStatsTimer()
        stopChatTimer()
        stopViewerCountTimer() // 停止观看人数跳动定时器

        // 注意：不重置统计数据，保留用于分析
        // stats.viewerCount = 0
        // stats.duration = 0
        // chatMessages.removeAll()
    }
    
    // MARK: - 定时器管理
    private func startStatsTimer() {
        // 统计更新定时器
        statsTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateStats()
        }
    }
    
    private func stopStatsTimer() {
        statsTimer?.invalidate()
        statsTimer = nil
    }

    private func startChatTimer() {
        // 弹幕生成定时器 - 加快弹幕生成频率
        chatTimer = Timer.scheduledTimer(withTimeInterval: 1.2, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.generateChatMessage()
            }
        }
    }

    private func stopChatTimer() {
        chatTimer?.invalidate()
        chatTimer = nil
    }

    private func startViewerCountTimer() {
        // 观看人数跳动定时器，3-5秒随机间隔
        scheduleNextViewerCountUpdate()
    }

    private func stopViewerCountTimer() {
        viewerCountTimer?.invalidate()
        viewerCountTimer = nil
    }

    private func scheduleNextViewerCountUpdate() {
        // 随机间隔3-5秒
        let interval = Double.random(in: 3.0...5.0)

        viewerCountTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: false) { [weak self] _ in
            self?.updateViewerCount()
            self?.scheduleNextViewerCountUpdate() // 递归调度下一次更新
        }
    }

    private func updateViewerCount() {
        DispatchQueue.main.async { [weak self] in
            // 8万到10万+之间随机跳动
            let randomValue = Int.random(in: 80000...110000) // 包含超过10万的情况
            self?.stats.viewerCount = randomValue
        }
    }
    
    // MARK: - 统计更新
    private func updateStats() {
        guard let startTime = startTime else { return }

        DispatchQueue.main.async {
            // 更新直播时长
            self.stats.duration = Date().timeIntervalSince(startTime)

            // 点赞数据递增：每秒随机50-1000
            let likeIncrement = Int.random(in: 50...1000)
            self.stats.likeCount = min(1000000, self.stats.likeCount + likeIncrement) // 最大100万

            // 观看人数现在由专门的定时器管理，这里不再更新
        }
    }

    // MARK: - 弹幕生成
    @MainActor
    private func generateChatMessage() {
        let username = ChatContent.usernames.randomElement() ?? "用户"

        // 80%概率生成AI相关的产品问题，20%概率生成普通弹幕或来了弹幕
        let randomValue = Int.random(in: 1...100)
        let message: String

        if randomValue <= 80 {
            // 80%概率：优先使用AI生成的真实问题，如果没有则使用预设问题
            if let aiQuestions = ecommerceManager?.customerQuestions,
               !aiQuestions.isEmpty,
               let randomQuestion = aiQuestions.randomElement() {
                message = randomQuestion.question
            } else {
                // 如果没有AI生成的问题，使用预设的产品问题
                message = ChatContent.productQuestions.randomElement() ?? "这个产品怎么样？"
            }
        } else if randomValue <= 90 {
            // 10%概率：来了类型的弹幕
            message = ChatContent.arrivedMessages.randomElement() ?? "来了来了！"
        } else {
            // 10%概率：普通弹幕
            message = ChatContent.normalMessages.randomElement() ?? "666"
        }

        let chatMessage = ChatMessage(
            username: username,
            message: message,
            timestamp: Date(),
            messageType: .normal,
            userLevel: Int.random(in: 1...30)
        )

        DispatchQueue.main.async {
            self.chatMessages.append(chatMessage)

            // 保持最多20条消息
            if self.chatMessages.count > 20 {
                self.chatMessages.removeFirst()
            }
        }
    }
}
