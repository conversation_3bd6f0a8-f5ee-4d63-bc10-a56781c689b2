//
//  SettingsView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI
import PhotosUI

struct SettingsView: View {
    @EnvironmentObject var liveStreamManager: LiveStreamManager
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject var userSettings: UserSettings

    // 用户名编辑状态
    @State private var isEditingUsername = false
    @State private var tempUsername = ""
    @FocusState private var isUsernameFieldFocused: Bool

    // 头像选择状态
    @State private var showingAvatarPicker = false
    @State private var selectedAvatar = ""
    @State private var showingImagePicker = false
    @State private var selectedImageData: Data? = nil

    // 预设头像选项
    private let avatarOptions = ["👤", "😊", "😎", "🤩", "🥰", "😇", "🤗", "🤔", "😋", "🤪", "🥳", "🤠", "👨‍💼", "👩‍💼", "👨‍🎤", "👩‍🎤"]
    
    var body: some View {
        ZStack {
            // 背景
            Color.black.ignoresSafeArea()

            ScrollView {
                VStack(spacing: 0) {
                    // 头部
                    headerSection

                    // 主播配置
                    streamerConfigSection

                    // 开始直播按钮
                    startLiveButton
                }
            }
        }
        .sheet(isPresented: $showingAvatarPicker) {
            avatarPickerSheet
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(selectedImageData: $selectedImageData)
        }
        .onAppear {
            selectedAvatar = userSettings.displayAvatar
        }
        .onChange(of: selectedImageData) { _, imageData in
            if let data = imageData {
                userSettings.setCustomImageAvatar(data)
            }
        }
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(spacing: 8) {
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .foregroundColor(.white)
                }

                Spacer()

                VStack(spacing: 4) {
                    Text("稳啦练直播")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("v0.0.1")
                        .font(.caption)
                        .foregroundColor(.gray)
                }

                Spacer()

                // 移除设置按钮，只保留占位空间
                Color.clear
                    .frame(width: 24, height: 24)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
        }
    }
    
    // MARK: - 主播配置
    private var streamerConfigSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            sectionHeader("主播配置")

            // 头像设置
            avatarSettingRow

            // 用户名设置
            usernameSettingRow
        }
        .padding(.top, 30)
    }

    // MARK: - 头像设置行
    private var avatarSettingRow: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("头像")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 20)

            Button(action: {
                showingAvatarPicker = true
            }) {
                HStack(spacing: 12) {
                    // 当前头像显示
                    if userSettings.isUsingCustomImageAvatar, let uiImage = userSettings.getSafeAvatarImage() {
                        // 显示自定义图片头像
                        Circle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 50, height: 50)
                            .overlay(
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 50, height: 50)
                                    .clipShape(Circle())
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.blue.opacity(0.6), lineWidth: 2)
                            )
                    } else if userSettings.isUsingDefaultAvatar {
                        // 显示默认emoji头像
                        Circle()
                            .fill(Color.gray.opacity(0.6))
                            .frame(width: 50, height: 50)
                            .overlay(
                                Text(userSettings.displayAvatar)
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    } else {
                        // 显示选择的emoji头像
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.green, Color.blue]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 50, height: 50)
                            .overlay(
                                Text(userSettings.displayAvatar)
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text("点击选择头像")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)

                        Text("选择表情或从相册选择图片")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal, 20)
            }
        }
    }

    // MARK: - 用户名设置行
    private var usernameSettingRow: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("用户名")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 20)

            if isEditingUsername {
                // 编辑模式
                HStack(spacing: 12) {
                    TextField("输入用户名", text: $tempUsername)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                        .focused($isUsernameFieldFocused)
                        .onAppear {
                            tempUsername = userSettings.username
                        }

                    // 确认按钮
                    Button(action: {
                        if !tempUsername.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            userSettings.username = tempUsername.trimmingCharacters(in: .whitespacesAndNewlines)
                        }
                        isUsernameFieldFocused = false
                        isEditingUsername = false
                    }) {
                        Image(systemName: "checkmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.green)
                            .padding(8)
                            .background(Color.green.opacity(0.2))
                            .clipShape(Circle())
                    }

                    // 取消按钮
                    Button(action: {
                        isUsernameFieldFocused = false
                        isEditingUsername = false
                        tempUsername = userSettings.username
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.red)
                            .padding(8)
                            .background(Color.red.opacity(0.2))
                            .clipShape(Circle())
                    }
                }
                .padding(.horizontal, 20)
            } else {
                // 显示模式
                Button(action: {
                    isEditingUsername = true
                    tempUsername = userSettings.username
                    // 延迟一点时间让UI更新完成后再聚焦
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isUsernameFieldFocused = true
                    }
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "person.text.rectangle")
                            .font(.system(size: 20))
                            .foregroundColor(.blue)
                            .frame(width: 30)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("当前用户名")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)

                            Text(userSettings.username)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        }

                        Spacer()

                        Image(systemName: "pencil")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                }
            }
        }
    }

    // MARK: - 开始直播按钮
    private var startLiveButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Text("保存设置并返回")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.pink, Color.purple]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
        }
        .padding(.horizontal, 20)
        .padding(.top, 30)
        .padding(.bottom, 40)
    }

    // MARK: - 头像选择器弹窗
    private var avatarPickerSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("选择头像")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)

                Text("选择表情或从相册选择图片")
                    .font(.subheadline)
                    .foregroundColor(.gray)

                // 从相册选择按钮
                Button(action: {
                    showingAvatarPicker = false
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        showingImagePicker = true
                    }
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 20))
                            .foregroundColor(.blue)

                        Text("从相册选择")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.blue)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
                .padding(.horizontal)

                Text("或选择表情头像")
                    .font(.subheadline)
                    .foregroundColor(.gray)

                // 头像网格
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
                    ForEach(avatarOptions, id: \.self) { avatar in
                        Button(action: {
                            selectedAvatar = avatar
                        }) {
                            Circle()
                                .fill(selectedAvatar == avatar ?
                                      LinearGradient(gradient: Gradient(colors: [Color.green, Color.blue]),
                                                   startPoint: .topLeading,
                                                   endPoint: .bottomTrailing) :
                                      LinearGradient(gradient: Gradient(colors: [Color.gray.opacity(0.3)]),
                                                   startPoint: .topLeading,
                                                   endPoint: .bottomTrailing))
                                .frame(width: 60, height: 60)
                                .overlay(
                                    Text(avatar)
                                        .font(.system(size: 30))
                                )
                                .overlay(
                                    Circle()
                                        .stroke(selectedAvatar == avatar ? Color.blue : Color.gray.opacity(0.3),
                                               lineWidth: selectedAvatar == avatar ? 3 : 1)
                                )
                                .scaleEffect(selectedAvatar == avatar ? 1.1 : 1.0)
                                .animation(.spring(response: 0.3), value: selectedAvatar)
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()

                // 确认按钮
                Button(action: {
                    userSettings.setEmojiAvatar(selectedAvatar)
                    showingAvatarPicker = false
                }) {
                    Text("确认选择表情")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(Color.blue)
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("取消") {
                    showingAvatarPicker = false
                }
            )
        }
    }

    // MARK: - 辅助视图
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(.system(size: 18, weight: .bold))
            .foregroundColor(.white)
            .padding(.horizontal, 20)
    }
}

#Preview {
    SettingsView(userSettings: UserSettings())
        .environmentObject(LiveStreamManager())
}
