//
//  WelcomeView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI

struct WelcomeView: View {
    @Binding var showWelcome: Bool
    @ObservedObject var userSettings: UserSettings
    @State private var animateGradient = false
    @State private var showContent = false
    @State private var showSettings = false
    @State private var showAPITest = false
    
    var body: some View {
        ZStack {
            // 动态背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple,
                    Color.pink,
                    Color.orange,
                    Color.red
                ]),
                startPoint: animateGradient ? .topLeading : .bottomTrailing,
                endPoint: animateGradient ? .bottomTrailing : .topLeading
            )
            .ignoresSafeArea()
            .onAppear {
                withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
                    animateGradient = true
                }
            }
            
            // 设置按钮
            VStack {
                HStack {
                    // API测试按钮
                    Button(action: {
                        showAPITest = true
                    }) {
                        Image(systemName: "network")
                            .foregroundColor(.white)
                            .font(.title2)
                            .padding()
                            .background(Color.white.opacity(0.2))
                            .clipShape(Circle())
                    }
                    .padding(.leading)

                    Spacer()

                    // 设置按钮
                    Button(action: {
                        showSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                            .foregroundColor(.white)
                            .font(.title2)
                            .padding()
                            .background(Color.white.opacity(0.2))
                            .clipShape(Circle())
                    }
                    .padding(.trailing)
                }
                .padding(.top)
                Spacer()
            }

            // 内容
            VStack(spacing: 40) {
                Spacer()

                // Logo区域
                VStack(spacing: 20) {
                    // App图标
                    ZStack {
                        Circle()
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: "video.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.white)
                    }
                    .scaleEffect(showContent ? 1.0 : 0.5)
                    .opacity(showContent ? 1.0 : 0.0)
                    
                    // 标题
                    VStack(spacing: 12) {
                        Text("直播卖货热身训练")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("赚钱是经过专门训练获得技能之后的自然结果")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .opacity(showContent ? 1.0 : 0.0)
                    .offset(y: showContent ? 0 : 50)
                }
                
                Spacer()
                
                // 开始按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        showWelcome = false
                    }
                }) {
                    Text("开始练习")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 25)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .padding(.horizontal, 40)
                .scaleEffect(showContent ? 1.0 : 0.8)
                .opacity(showContent ? 1.0 : 0.0)
                
                Spacer()
            }
            .padding()
        }
        .onAppear {
            withAnimation(.easeOut(duration: 1.0).delay(0.5)) {
                showContent = true
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView(userSettings: userSettings)
        }
        .sheet(isPresented: $showAPITest) {
            APITestView()
        }
    }
}

#Preview {
    WelcomeView(showWelcome: .constant(true), userSettings: UserSettings())
}
