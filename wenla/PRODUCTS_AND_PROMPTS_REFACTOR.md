# 商品信息和AI提示词重构总结

## 概述
为了更好地管理商品信息和AI提示词，我们创建了一个专门的文件 `ProductsAndPrompts.swift` 来集中管理这些数据，使代码更加模块化和易于维护。

## 新增文件

### ProductsAndPrompts.swift
这个文件包含两个主要的管理器：

#### 1. ProductDataManager
负责管理所有商品数据：

**功能特性：**
- 集中管理快捷商品数据
- 提供商品分类查询功能
- 支持根据ID查找特定商品
- 易于扩展新商品

**当前商品：**
1. **Phala稳稳垫** - 智能瑜伽垫（运动健身类）
2. **智能蓝牙耳机** - 降噪耳机（数码电子类）
3. **护肤精华套装** - 美白淡斑套装（美妆护肤类）

**主要方法：**
```swift
// 获取所有快捷商品
static let quickProducts: [QuickProduct]

// 根据类别获取商品
static func getProductsByCategory(_ category: String) -> [QuickProduct]

// 获取所有商品类别
static func getAllCategories() -> [String]

// 根据ID获取商品
static func getProduct(by id: UUID) -> QuickProduct?
```

#### 2. AIPromptManager
负责管理所有AI相关的提示词：

**功能特性：**
- 商品分析提示词生成
- 直播表现分析提示词生成
- 根据商品类别生成备用问题
- 统一管理测试连接提示词

**主要方法：**
```swift
// 商品分析提示词（生成50个客户问题）
static func getProductAnalysisPrompt(for product: ProductInfo) -> String

// 直播表现分析提示词
static func getLiveStreamAnalysisPrompt(...) -> String

// 测试AI连接提示词
static let testConnectionPrompt: String

// 备用问题生成（根据商品类别）
static func getFallbackQuestionsPrompt(for productCategory: String) -> [String]
```

## 修改的文件

### 1. ProductInputView.swift
**变更：**
- 移除了硬编码的商品数据
- 使用 `ProductDataManager.quickProducts` 获取商品列表
- 代码更加简洁，便于维护

### 2. ECommerceManager.swift
**变更：**
- 使用 `AIPromptManager.getProductAnalysisPrompt()` 生成商品分析提示词
- 使用 `AIPromptManager.getLiveStreamAnalysisPrompt()` 生成直播分析提示词
- 使用 `AIPromptManager.testConnectionPrompt` 进行连接测试
- 使用 `AIPromptManager.getFallbackQuestionsPrompt()` 生成备用问题

## 优势

### 1. 代码组织更清晰
- 商品数据和AI提示词分离到专门文件
- 业务逻辑和数据管理分离
- 更好的代码可读性和维护性

### 2. 易于扩展
- **添加新商品：** 只需在 `ProductDataManager.quickProducts` 中添加新的 `QuickProduct` 实例
- **修改提示词：** 只需修改 `AIPromptManager` 中对应的方法
- **添加新类别：** 自动支持，无需额外配置

### 3. 便于维护
- 集中管理，避免重复代码
- 修改提示词不需要在多个文件中查找
- 商品信息统一管理，保证一致性

### 4. 类型安全
- 使用结构化的数据模型
- 编译时检查，减少运行时错误
- 清晰的接口定义

## 如何添加新商品

在 `ProductsAndPrompts.swift` 文件的 `ProductDataManager.quickProducts` 数组中添加新的商品：

```swift
QuickProduct(
    name: "新商品名称",
    description: "简短描述",
    icon: "SF Symbol图标名",
    iconColor: .颜色,
    productInfo: ProductInfo(
        name: "完整商品名称",
        description: "详细描述...",
        price: "¥价格",
        imageURL: "图片链接",
        features: [
            "特性1",
            "特性2",
            // ...
        ],
        category: "商品类别"
    )
)
```

## 如何修改AI提示词

在 `ProductsAndPrompts.swift` 文件的 `AIPromptManager` 中修改对应的方法：

1. **商品分析提示词：** 修改 `getProductAnalysisPrompt(for:)` 方法
2. **直播分析提示词：** 修改 `getLiveStreamAnalysisPrompt(...)` 方法
3. **备用问题：** 修改 `getFallbackQuestionsPrompt(for:)` 方法

## 编译状态
✅ 所有修改已完成，项目编译成功，无错误和警告（除了一些可忽略的 Sendable 警告）

## 下一步建议

1. **扩展商品库：** 可以添加更多不同类别的商品
2. **优化提示词：** 根据实际使用效果调整AI提示词
3. **数据持久化：** 考虑将商品数据存储到本地文件或数据库
4. **动态加载：** 支持从服务器动态加载商品信息
5. **A/B测试：** 支持不同版本的提示词进行效果对比
