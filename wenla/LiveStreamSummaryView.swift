//
//  LiveStreamSummaryView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import SwiftUI

struct LiveStreamSummaryView: View {
    @Binding var isPresented: Bool
    let summary: LiveStreamSummary
    var onComplete: (() -> Void)? = nil
    @State private var showTranscriptDetail = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题
                    VStack(spacing: 8) {
                        Image(systemName: "chart.bar.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.green)
                        
                        Text("直播总结报告")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    }
                    .padding(.top)
                    
                    // 基础数据
                    VStack(spacing: 16) {
                        Text("直播数据")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            StatCard(
                                title: "直播时长",
                                value: formatDuration(summary.duration),
                                icon: "clock.fill",
                                color: .blue
                            )

                            StatCard(
                                title: "观看人数",
                                value: formatNumber(summary.stats.viewerCount),
                                icon: "eye.fill",
                                color: .green
                            )

                            StatCard(
                                title: "峰值观众",
                                value: formatNumber(summary.stats.peakViewers),
                                icon: "chart.line.uptrend.xyaxis",
                                color: .orange
                            )

                            StatCard(
                                title: "点赞数",
                                value: formatNumber(summary.stats.likeCount),
                                icon: "heart.fill",
                                color: .red
                            )

                            StatCard(
                                title: "评论数",
                                value: formatNumber(summary.stats.commentCount),
                                icon: "bubble.left.fill",
                                color: .cyan
                            )

                            StatCard(
                                title: "新增粉丝",
                                value: formatNumber(summary.stats.newFollowers),
                                icon: "person.badge.plus.fill",
                                color: .purple
                            )

                            StatCard(
                                title: "分享次数",
                                value: formatNumber(summary.stats.shareCount),
                                icon: "square.and.arrow.up.fill",
                                color: .indigo
                            )

                            StatCard(
                                title: "礼物数量",
                                value: formatNumber(summary.stats.giftCount),
                                icon: "gift.fill",
                                color: .pink
                            )

                            StatCard(
                                title: "平均观众",
                                value: formatNumber(summary.stats.averageViewers),
                                icon: "person.3.fill",
                                color: .teal
                            )
                        }
                    }
                    .padding(.horizontal)

                    // 问答数据
                    VStack(spacing: 16) {
                        Text("问答统计")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 16) {
                            StatCard(
                                title: "总问题数",
                                value: "\(summary.totalQuestions)",
                                icon: "questionmark.circle.fill",
                                color: .orange
                            )

                            StatCard(
                                title: "已回答",
                                value: "\(summary.answeredQuestions)",
                                icon: "checkmark.circle.fill",
                                color: .green
                            )

                            StatCard(
                                title: "回答率",
                                value: summary.totalQuestions > 0 ? String(format: "%.1f%%", Double(summary.answeredQuestions) / Double(summary.totalQuestions) * 100) : "0%",
                                icon: "percent",
                                color: .purple
                            )

                            StatCard(
                                title: "转录条数",
                                value: "\(summary.transcript.count)",
                                icon: "text.bubble.fill",
                                color: .blue
                            )
                        }
                    }
                    .padding(.horizontal)

                    // 转录内容预览
                    VStack(spacing: 16) {
                        HStack {
                            Text("直播转录")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Spacer()

                            Button(action: {
                                showTranscriptDetail = true
                            }) {
                                HStack(spacing: 4) {
                                    Text("查看全部")
                                        .font(.subheadline)
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                }
                                .foregroundColor(.blue)
                            }
                        }

                        VStack(spacing: 8) {
                            ForEach(Array(summary.transcript.prefix(3)), id: \.id) { segment in
                                HStack(alignment: .top, spacing: 8) {
                                    Text(formatTimestamp(segment.timestamp))
                                        .font(.caption2)
                                        .foregroundColor(.blue)
                                        .frame(width: 40, alignment: .leading)

                                    Text(segment.speaker)
                                        .font(.caption2)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(segment.speaker == "主播" ? Color.blue : Color.green)
                                        .cornerRadius(8)

                                    Text(segment.content)
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                        .lineLimit(2)

                                    Spacer()
                                }
                                .padding(.vertical, 4)
                            }

                            if summary.transcript.count > 3 {
                                Text("还有 \(summary.transcript.count - 3) 条记录...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                    }
                    .padding(.horizontal)

                    // 表现评分
                    VStack(spacing: 16) {
                        Text("表现分析")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        VStack(spacing: 16) {
                            // 综合评分
                            VStack(spacing: 12) {
                                HStack {
                                    Text("综合评分")
                                        .font(.body)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(String(format: "%.1f", summary.performanceScore))
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(scoreColor(summary.performanceScore))
                                    Text("/ 100")
                                        .font(.body)
                                        .foregroundColor(.secondary)
                                }

                                ProgressView(value: summary.performanceScore / 100)
                                    .progressViewStyle(LinearProgressViewStyle(tint: scoreColor(summary.performanceScore)))
                                    .scaleEffect(x: 1, y: 2, anchor: .center)
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(12)

                            // 详细分析
                            VStack(spacing: 8) {
                                PerformanceRow(title: "互动水平", score: summary.performanceAnalysis.interactionLevel)
                                PerformanceRow(title: "内容质量", score: summary.performanceAnalysis.contentQuality)
                                PerformanceRow(title: "专业程度", score: summary.performanceAnalysis.professionalLevel)
                                PerformanceRow(title: "响应速度", score: summary.performanceAnalysis.responseSpeed)
                            }
                            .padding()
                            .background(Color.gray.opacity(0.05))
                            .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal)

                    // 关键时刻
                    VStack(spacing: 16) {
                        Text("关键时刻")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        VStack(spacing: 8) {
                            ForEach(Array(summary.keyMoments.enumerated()), id: \.offset) { index, moment in
                                HStack(spacing: 12) {
                                    Text(formatTimestamp(moment.0))
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)
                                        .frame(width: 50, alignment: .leading)

                                    Circle()
                                        .fill(Color.blue)
                                        .frame(width: 6, height: 6)

                                    Text(moment.1)
                                        .font(.body)
                                        .foregroundColor(.primary)

                                    Spacer()
                                }
                                .padding(.vertical, 4)
                            }
                        }
                        .padding()
                        .background(Color.blue.opacity(0.05))
                        .cornerRadius(12)
                    }
                    .padding(.horizontal)

                    // 精彩片段
                    VStack(spacing: 16) {
                        Text("精彩片段")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        VStack(spacing: 8) {
                            ForEach(Array(summary.highlights.enumerated()), id: \.offset) { index, highlight in
                                HStack(alignment: .top, spacing: 12) {
                                    Image(systemName: "star.fill")
                                        .font(.caption)
                                        .foregroundColor(.yellow)
                                        .frame(width: 16)

                                    Text(highlight)
                                        .font(.body)
                                        .foregroundColor(.primary)
                                        .multilineTextAlignment(.leading)

                                    Spacer()
                                }
                                .padding(.vertical, 4)
                            }
                        }
                        .padding()
                        .background(Color.yellow.opacity(0.1))
                        .cornerRadius(12)
                    }
                    .padding(.horizontal)

                    // 优化建议
                    VStack(spacing: 16) {
                        Text("优化建议")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        VStack(spacing: 12) {
                            ForEach(Array(summary.suggestions.enumerated()), id: \.offset) { index, suggestion in
                                HStack(alignment: .top, spacing: 12) {
                                    Text("\(index + 1)")
                                        .font(.caption)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .frame(width: 20, height: 20)
                                        .background(Color.blue)
                                        .clipShape(Circle())
                                    
                                    Text(suggestion)
                                        .font(.body)
                                        .foregroundColor(.primary)
                                        .multilineTextAlignment(.leading)
                                    
                                    Spacer()
                                }
                                .padding()
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(12)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // 关闭按钮
                    Button(action: {
                        isPresented = false
                        onComplete?()
                    }) {
                        Text("完成")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(Color.blue)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationBarHidden(true)
            .sheet(isPresented: $showTranscriptDetail) {
                TranscriptDetailView(transcript: summary.transcript, duration: summary.duration)
            }
        }
    }
    
    // MARK: - 辅助方法
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let minutes = Int(timestamp) / 60
        let seconds = Int(timestamp) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private func formatNumber(_ number: Int) -> String {
        if number >= 10000 {
            return String(format: "%.1f万", Double(number) / 10000)
        } else if number >= 1000 {
            return String(format: "%.1fk", Double(number) / 1000)
        } else {
            return "\(number)"
        }
    }

    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 90...100:
            return .green
        case 70..<90:
            return .orange
        default:
            return .red
        }
    }
}

// MARK: - 统计卡片
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(height: 20)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - 表现评分行
struct PerformanceRow: View {
    let title: String
    let score: Double

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            HStack(spacing: 8) {
                ProgressView(value: score / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: scoreColor(score)))
                    .frame(width: 80)

                Text(String(format: "%.0f", score))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(scoreColor(score))
                    .frame(width: 30, alignment: .trailing)
            }
        }
    }

    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 90...100:
            return .green
        case 70..<90:
            return .orange
        default:
            return .red
        }
    }
}

#Preview {
    LiveStreamSummaryView(
        isPresented: .constant(true),
        summary: LiveStreamSummary(
            duration: 1800,
            startTime: Date().addingTimeInterval(-1800),
            endTime: Date(),
            productName: "智能手机",
            productUrl: "https://example.com",
            stats: LiveStreamStats(
                viewerCount: 2500,
                peakViewers: 4200,
                averageViewers: 2100,
                likeCount: 1500,
                shareCount: 120,
                commentCount: 580,
                giftCount: 45,
                newFollowers: 180
            ),
            totalQuestions: 8,
            answeredQuestions: 6,
            topQuestions: ["这款手机多少钱？", "电池能用多久？"],
            transcript: [
                TranscriptSegment(timestamp: 120, speaker: "主播", content: "大家好，欢迎来到我的直播间！", confidence: 0.95),
                TranscriptSegment(timestamp: 135, speaker: "观众", content: "这款手机多少钱？", confidence: 0.88)
            ],
            fullTranscriptText: "完整转录文本",
            performanceAnalysis: PerformanceAnalysis(
                overallScore: 85.5,
                engagementScore: 82.0,
                contentQuality: 88.0,
                interactionLevel: 85.0,
                professionalLevel: 80.0,
                responseSpeed: 75.0
            ),
            suggestions: [
                "增加产品展示的细节，让观众更直观地了解产品特点",
                "回答问题时语速可以稍微放慢，确保观众能够听清楚"
            ],
            highlights: [
                "15:30 - 产品功能演示获得大量点赞",
                "22:45 - 回答电池续航问题引发热烈讨论"
            ],
            keyMoments: [
                (930, "开始产品演示"),
                (1365, "回答观众热门问题")
            ],
            performanceScore: 85.5
        )
    )
}
