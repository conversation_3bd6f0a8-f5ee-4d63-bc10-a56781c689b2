//
//  DoubaoAPITest.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/12.
//

import Foundation

// MARK: - 豆包API测试类
class DoubaoAPITest {
    private let apiKey = "e9e21053-6373-4315-b22f-196e5ab9fb04"
    private let model = "doubao-seed-1-6-250615"
    
    // MARK: - 测试豆包API连接
    func testDoubaoAPI() async {
        print("🧪 开始测试豆包API...")
        
        do {
            let response = try await callDoubaoAPI(prompt: "你好，请简单回复一句话确认连接成功")
            print("✅ 豆包API测试成功")
            print("📝 响应内容: \(response)")
        } catch {
            print("❌ 豆包API测试失败: \(error.localizedDescription)")
            
            if let nsError = error as NSError? {
                print("🔍 错误详情:")
                print("   - Domain: \(nsError.domain)")
                print("   - Code: \(nsError.code)")
                print("   - UserInfo: \(nsError.userInfo)")
            }
        }
    }
    
    // MARK: - 豆包API调用实现
    private func callDoubaoAPI(prompt: String) async throws -> String {
        let apiURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        
        guard let url = URL(string: apiURL) else {
            throw NSError(domain: "InvalidURL", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的API地址"])
        }
        
        let requestBody: [String: Any] = [
            "model": model,
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": 100,
            "temperature": 0.7,
            "stream": false
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("wenla-test/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 30
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw NSError(domain: "SerializationError", code: 0, userInfo: [NSLocalizedDescriptionKey: "请求序列化失败"])
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "InvalidResponse", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的响应"])
        }
        
        print("📡 HTTP状态码: \(httpResponse.statusCode)")
        
        if httpResponse.statusCode != 200 {
            let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
            print("❌ API错误响应: \(errorMessage)")
            throw NSError(domain: "APIError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API错误: \(errorMessage)"])
        }
        
        let responseString = String(data: data, encoding: .utf8) ?? "无法解码"
        print("📥 原始响应: \(responseString)")
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "ParseError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析响应"])
        }
        
        if let error = json["error"] as? [String: Any],
           let errorMessage = error["message"] as? String {
            throw NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "API错误: \(errorMessage)"])
        }
        
        guard let choices = json["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String else {
            throw NSError(domain: "ParseError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析响应格式"])
        }
        
        return content.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - 测试扩展
extension DoubaoAPITest {
    
    // 测试产品分析功能
    func testProductAnalysis() async {
        print("🧪 开始测试产品分析功能...")
        
        let prompt = """
        请为以下商品生成5个客户可能关心的问题：
        
        商品名称：智能蓝牙耳机
        商品描述：采用最新的主动降噪技术，提供沉浸式音频体验
        商品价格：¥299
        
        请直接返回问题列表，每行一个问题。
        """
        
        do {
            let response = try await callDoubaoAPI(prompt: prompt)
            print("✅ 产品分析测试成功")
            print("📝 生成的问题: \(response)")
        } catch {
            print("❌ 产品分析测试失败: \(error.localizedDescription)")
        }
    }
    
    // 测试直播分析功能
    func testLiveStreamAnalysis() async {
        print("🧪 开始测试直播分析功能...")
        
        let prompt = """
        请分析以下直播带货表现并给出评分：
        
        直播时长：15分钟
        产品名称：智能蓝牙耳机
        客户问题数：8
        
        请给出整体评分（0-100分）和3条优化建议。
        """
        
        do {
            let response = try await callDoubaoAPI(prompt: prompt)
            print("✅ 直播分析测试成功")
            print("📝 分析结果: \(response)")
        } catch {
            print("❌ 直播分析测试失败: \(error.localizedDescription)")
        }
    }
}
