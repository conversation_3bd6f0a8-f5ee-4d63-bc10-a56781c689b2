//
//  APITestView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/12.
//

import SwiftUI

struct APITestView: View {
    @StateObject private var aiManager = AIModelManager()
    @State private var testResult = ""
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("豆包API测试")
                    .font(.title)
                    .fontWeight(.bold)
                
                VStack(alignment: .leading, spacing: 10) {
                    Text("当前模型: \(aiManager.currentModel.displayName)")
                        .font(.headline)
                    
                    Text("API状态: \(aiManager.isProcessing ? "处理中..." : "就绪")")
                        .foregroundColor(aiManager.isProcessing ? .orange : .green)
                    
                    if aiManager.isUsingFallback {
                        Text("⚠️ 使用降级模式")
                            .foregroundColor(.orange)
                    }
                    
                    if let errorMessage = aiManager.errorMessage {
                        Text("错误: \(errorMessage)")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
                
                VStack(spacing: 15) {
                    Button(action: testNetworkDiagnostics) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("网络诊断")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isLoading)

                    Button(action: testBasicConnection) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("测试基本连接")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isLoading)
                    
                    Button(action: testProductAnalysis) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("测试产品分析")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isLoading)
                    
                    Button(action: testLiveStreamAnalysis) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("测试直播分析")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isLoading)
                }
                
                ScrollView {
                    Text(testResult)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color.black.opacity(0.05))
                        .cornerRadius(10)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("API测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 测试方法

    private func testNetworkDiagnostics() {
        isLoading = true
        testResult = "开始网络诊断...\n"

        Task {
            // 测试基本网络连接
            testResult += "1. 测试基本网络连接...\n"
            let networkOK = await aiManager.testNetworkConnectivity()

            await MainActor.run {
                if networkOK {
                    testResult += "✅ 基本网络连接正常\n"
                } else {
                    testResult += "❌ 基本网络连接失败\n"
                    testResult += "请检查网络设置\n\n"
                    isLoading = false
                    return
                }
            }

            // 测试豆包API端点
            testResult += "2. 测试豆包API端点...\n"
            let workingEndpoint = await aiManager.testDoubaoEndpoint()

            await MainActor.run {
                if let endpoint = workingEndpoint {
                    testResult += "✅ 找到可用端点: \(endpoint)\n"
                } else {
                    testResult += "❌ 所有豆包API端点都无法访问\n"
                    testResult += "可能的原因:\n"
                    testResult += "- API服务暂时不可用\n"
                    testResult += "- 网络防火墙阻止访问\n"
                    testResult += "- DNS解析问题\n"
                }

                testResult += "\n诊断完成\n\n"
                isLoading = false
            }
        }
    }

    private func testBasicConnection() {
        isLoading = true
        testResult = "开始测试基本连接...\n"
        
        Task {
            do {
                let response = try await aiManager.callAI(prompt: "你好，请简单回复确认连接成功")
                
                await MainActor.run {
                    testResult += "✅ 连接成功!\n"
                    testResult += "响应: \(response)\n"
                    testResult += "响应时间: \(Date().formatted(.dateTime.hour().minute().second()))\n\n"
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    testResult += "❌ 连接失败: \(error.localizedDescription)\n\n"
                    isLoading = false
                }
            }
        }
    }
    
    private func testProductAnalysis() {
        isLoading = true
        testResult = "开始测试产品分析...\n"
        
        let prompt = """
        请为以下商品生成3个客户可能关心的问题：
        
        商品名称：智能蓝牙耳机
        商品描述：采用最新的主动降噪技术
        商品价格：¥299
        
        请直接返回问题列表。
        """
        
        Task {
            do {
                let response = try await aiManager.callAI(prompt: prompt)
                
                await MainActor.run {
                    testResult += "✅ 产品分析成功!\n"
                    testResult += "生成的问题:\n\(response)\n\n"
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    testResult += "❌ 产品分析失败: \(error.localizedDescription)\n\n"
                    isLoading = false
                }
            }
        }
    }
    
    private func testLiveStreamAnalysis() {
        isLoading = true
        testResult = "开始测试直播分析...\n"
        
        let prompt = """
        请分析以下直播表现并给出评分：
        
        直播时长：10分钟
        产品名称：智能蓝牙耳机
        客户问题数：5
        
        请给出评分和2条建议。
        """
        
        Task {
            do {
                let response = try await aiManager.callAI(prompt: prompt)
                
                await MainActor.run {
                    testResult += "✅ 直播分析成功!\n"
                    testResult += "分析结果:\n\(response)\n\n"
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    testResult += "❌ 直播分析失败: \(error.localizedDescription)\n\n"
                    isLoading = false
                }
            }
        }
    }
}

#Preview {
    APITestView()
}
