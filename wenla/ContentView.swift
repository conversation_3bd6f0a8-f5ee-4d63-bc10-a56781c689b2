//
//  ContentView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var liveStreamManager = LiveStreamManager()
    @StateObject private var userSettings = UserSettings()
    @State private var showWelcome = true

    var body: some View {
        ZStack {
            if showWelcome {
                WelcomeView(showWelcome: $showWelcome, userSettings: userSettings)
                    .transition(.opacity)
            } else {
                NavigationView {
                    // 主直播界面
                    LiveStreamView(userSettings: userSettings, onClose: {
                        // 返回主页面的回调
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showWelcome = true
                        }
                    })
                    .environmentObject(liveStreamManager)
                }
                .navigationBarHidden(true)
                .transition(.move(edge: .trailing))
            }
        }
    }
}

#Preview {
    ContentView()
}
