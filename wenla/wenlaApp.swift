//
//  wenlaApp.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI
import UIKit

@main
struct wenlaApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    // App启动时禁用自动锁屏
                    UIApplication.shared.isIdleTimerDisabled = true
                    print("🔒 已禁用自动锁屏")
                }
                .onDisappear {
                    // App关闭时恢复自动锁屏
                    UIApplication.shared.isIdleTimerDisabled = false
                    print("🔓 已恢复自动锁屏")
                }
        }
    }
}

// MARK: - App Delegate
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // App启动完成时禁用自动锁屏
        application.isIdleTimerDisabled = true
        print("🚀 App启动完成，已禁用自动锁屏")
        return true
    }

    func applicationWillResignActive(_ application: UIApplication) {
        // App即将进入后台时恢复自动锁屏
        application.isIdleTimerDisabled = false
        print("⏸️ App进入后台，已恢复自动锁屏")
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        // App重新激活时禁用自动锁屏
        application.isIdleTimerDisabled = true
        print("▶️ App重新激活，已禁用自动锁屏")
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // App即将终止时恢复自动锁屏
        application.isIdleTimerDisabled = false
        print("🛑 App即将终止，已恢复自动锁屏")
    }
}
