# 分批请求实现总结

## 📅 实现时间
2025-07-12

## 🎯 实现目标
根据用户建议，将商品分析从单次请求50个问题改为分批请求：
- **分10次请求**：每次生成5个问题
- **进度显示**：每批占用一定进度，总共50个问题
- **避免超时**：单次请求简单，避免复杂分析导致的超时

## 🔧 核心实现

### 1. 分批类别定义
```swift
// MARK: - 获取分批类别
static func getBatchCategories() -> [String] {
    return [
        "产品功能和特性",
        "质量和耐用性", 
        "使用体验和效果",
        "性价比分析",
        "售后服务保障",
        "产品对比优势",
        "适用场景人群",
        "购买决策建议",
        "用户评价反馈",
        "常见问题解答"
    ]
}
```

### 2. 分批提示词优化
```swift
static func getProductAnalysisPrompt(for product: ProductInfo, batch: Int, category: String) -> String {
    return """
    请为以下商品生成5个关于\(category)的客户问题：

    商品：\(product.name)
    价格：\(product.price)
    特点：\(product.features.joined(separator: "、"))

    专注于\(category)方面，生成5个客户最关心的问题。

    要求：
    - 问题要实用，像真实用户会问的
    - 表达自然，便于直播回答
    - 每行一个问题，不需要编号
    - 专注于\(category)相关内容
    """
}
```

### 3. 分批请求逻辑
```swift
private func generateCustomerQuestions(for product: ProductInfo) async throws -> [CustomerQuestion] {
    var allQuestions: [CustomerQuestion] = []
    let categories = AIPromptManager.getBatchCategories()
    let totalBatches = categories.count
    
    print("🔄 开始分批生成问题，共\(totalBatches)批，每批5个问题")
    
    for (index, category) in categories.enumerated() {
        let batchNumber = index + 1
        print("📝 正在生成第\(batchNumber)/\(totalBatches)批问题：\(category)")
        
        // 更新进度：每批占2%进度 (35%-55%)
        let progress = 0.35 + (Double(index) / Double(totalBatches)) * 0.20
        dynamicProgress.setProgress(progress, description: "生成\(category)相关问题...")
        
        do {
            let prompt = AIPromptManager.getProductAnalysisPrompt(for: product, batch: batchNumber, category: category)
            let response = try await aiModelManager.callAI(prompt: prompt)
            let batchQuestions = parseBatchQuestionsFromResponse(response, category: category, batchIndex: index)
            
            allQuestions.append(contentsOf: batchQuestions)
            print("✅ 第\(batchNumber)批完成，生成\(batchQuestions.count)个问题")
            
            // 批次间短暂延迟，避免请求过于频繁
            try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒
            
        } catch {
            print("⚠️ 第\(batchNumber)批生成失败: \(error.localizedDescription)")
            // 如果某批失败，生成该类别的备用问题
            let fallbackQuestions = generateFallbackQuestionsForCategory(category, batchIndex: index)
            allQuestions.append(contentsOf: fallbackQuestions)
        }
    }
    
    print("🎉 问题生成完成，共生成\(allQuestions.count)个问题")
    return allQuestions
}
```

## 📊 实现效果

### 请求方式对比
| 项目 | 优化前 | 优化后 |
|------|--------|--------|
| 请求次数 | 1次 | 10次 |
| 每次问题数 | 50个 | 5个 |
| 单次复杂度 | 极高 | 低 |
| 超时风险 | 高 | 低 |
| 进度显示 | 粗糙 | 精细 |

### 进度分配
- **0-35%**：初始化和商品信息解析
- **35-55%**：分批生成问题（10批，每批2%）
- **55-95%**：整理分析结果
- **95-100%**：完成分析

### 容错机制
1. **单批失败处理**：某批失败时使用该类别的备用问题
2. **网络延迟**：批次间0.2秒延迟，避免请求过于频繁
3. **进度更新**：实时更新进度和描述信息

## 🔍 技术细节

### 分批解析方法
```swift
private func parseBatchQuestionsFromResponse(_ response: String, category: String, batchIndex: Int) -> [CustomerQuestion] {
    let lines = response.components(separatedBy: .newlines)
        .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        .filter { !$0.isEmpty }

    var questions: [CustomerQuestion] = []

    for (index, line) in lines.enumerated() {
        // 清理问题文本
        let cleanQuestion = line
            .replacingOccurrences(of: "^\\d+[.、]\\s*", with: "", options: .regularExpression)
            .replacingOccurrences(of: "^[•-]\\s*", with: "", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)

        if !cleanQuestion.isEmpty && cleanQuestion.count > 5 {
            // 根据批次索引设置优先级
            let priority = (batchIndex < 3) ? 1 : ((batchIndex < 7) ? 2 : 3)

            questions.append(CustomerQuestion(
                question: cleanQuestion,
                category: category,
                priority: priority
            ))
        }

        // 每批最多5个问题
        if questions.count >= 5 {
            break
        }
    }

    return questions
}
```

### 备用问题生成
```swift
private func generateFallbackQuestionsForCategory(_ category: String, batchIndex: Int) -> [CustomerQuestion] {
    let fallbackQuestions: [String]
    
    switch category {
    case "产品功能和特性":
        fallbackQuestions = [
            "这个产品有什么主要功能？",
            "产品的核心特色是什么？",
            "使用方法复杂吗？",
            "有哪些实用的功能？",
            "产品技术先进吗？"
        ]
    // ... 其他类别
    }
    
    let priority = (batchIndex < 3) ? 1 : ((batchIndex < 7) ? 2 : 3)
    
    return fallbackQuestions.map { question in
        CustomerQuestion(
            question: question,
            category: category,
            priority: priority
        )
    }
}
```

## 🎉 用户体验提升

### 1. 详细进度显示
- 用户可以看到具体正在生成哪个类别的问题
- 进度条平滑更新，每批完成后立即反馈

### 2. 更高成功率
- 单次请求简单，大大降低超时风险
- 即使某批失败，其他批次仍可正常完成

### 3. 更快的初始反馈
- 第一批问题生成后用户就能看到部分结果
- 不需要等待所有50个问题都生成完毕

## 🧪 测试建议

### 功能测试
1. **完整流程**：测试10批请求是否都能正常完成
2. **问题质量**：验证生成的50个问题是否覆盖所有类别
3. **进度显示**：确认进度条和描述文字正确更新

### 性能测试
1. **总耗时**：测试完整分析流程的总时间
2. **网络适应性**：在不同网络环境下测试
3. **错误恢复**：模拟某批请求失败的情况

### 用户体验测试
1. **进度感知**：用户是否能清楚了解当前进度
2. **等待体验**：分批显示是否减少了等待焦虑
3. **结果满意度**：50个问题的质量和相关性

## 📈 预期效果
1. **超时问题解决**：分批请求避免单次请求过于复杂
2. **用户体验提升**：详细进度显示和更快反馈
3. **系统稳定性**：更好的容错机制和错误恢复
4. **问题数量达标**：确保生成50个高质量问题
