# 文拉 (Wenla) - 电商直播练习器

一个专业的电商直播练习应用，集成了AI智能分析、语音识别和实时互动功能，帮助主播提升直播带货技能。

## 🎯 核心功能

### 1. 智能电商分析
- **商品链接分析**：输入淘宝、京东、天猫等电商链接
- **AI问题生成**：基于DeepSeek R1模型分析商品，生成真实用户购买问题
- **模拟客户询单**：自动在弹幕中发送客户问题，模拟真实直播场景

### 2. 实时语音识别
- **中文语音识别**：实时转录主播的语音内容
- **直播转录**：记录整场直播的语音内容用于后续分析
- **权限管理**：自动请求麦克风和语音识别权限

### 3. 直播模拟环境
- **前置摄像头**：自动启动前置摄像头，模拟真实直播
- **弹幕系统**：实时显示观众消息和客户问题
- **点赞飘心**：连续自动点赞效果，营造热门直播氛围
- **观众数据**：模拟观看人数、点赞数等直播数据

### 4. AI直播总结
- **表现评分**：基于语音转录和互动数据给出综合评分
- **优化建议**：提供具体的话术和表现改进建议
- **数据分析**：统计问题回答率、直播时长等关键指标

## 🚀 使用流程

### 第一步：开始练习
1. 打开应用，点击"开始练习"
2. 允许摄像头和麦克风权限
3. 进入直播练习界面

### 第二步：添加商品
1. 点击底部"电商"按钮
2. 输入商品链接（支持淘宝、京东、天猫等）
3. 等待AI分析商品信息

### 第三步：开始练习
1. AI会自动生成客户问题并在弹幕中显示
2. 主播对着摄像头回答客户问题
3. 语音识别会实时记录主播的回答

### 第四步：查看总结
1. 结束直播后自动生成总结报告
2. 查看表现评分和优化建议
3. 根据建议改进下次直播表现

## 🛠 技术特性

### AI集成
- **DeepSeek R1模型**：用于商品分析和问题生成
- **智能问题库**：涵盖价格、质量、使用、售后等各个方面
- **个性化建议**：基于实际表现提供针对性改进建议

### 语音技术
- **实时识别**：基于iOS Speech框架的中文语音识别
- **高准确率**：针对直播场景优化的识别效果
- **离线支持**：支持设备本地语音识别

### 用户体验
- **简洁界面**：专注于直播练习的核心功能
- **流畅动画**：自然的点赞飘心和弹幕效果
- **权限管理**：智能的权限请求和错误处理

## 📱 系统要求

- iOS 18.5 或更高版本
- 支持前置摄像头的设备
- 麦克风权限
- 网络连接（用于AI分析）

## 🔧 配置说明

### DeepSeek API配置
1. 注册DeepSeek账号：https://api-docs.deepseek.com/zh-cn/
2. 获取API密钥
3. 在`ECommerceManager.swift`中替换`YOUR_DEEPSEEK_API_KEY`

```swift
private let deepSeekAPIKey = "YOUR_DEEPSEEK_API_KEY"
```

### 权限配置
应用会自动请求以下权限：
- 摄像头权限：用于直播预览
- 麦克风权限：用于语音识别
- 语音识别权限：用于转录功能

## 🎨 界面说明

### 主界面
- **顶部状态栏**：显示观看人数、点赞数等数据
- **左侧弹幕区**：显示观众消息和客户问题
- **右下角**：自动点赞飘心效果
- **底部控制栏**：PK、连线、互动、装饰、电商、更多

### 电商功能
- **商品输入**：支持主流电商平台链接
- **分析状态**：实时显示AI分析进度
- **问题生成**：自动生成5-8个客户问题

### 总结报告
- **基础数据**：直播时长、问题数量、回答率
- **表现评分**：0-100分的综合评分
- **优化建议**：3-5条具体改进建议

## 🔄 更新日志

### v1.0.0
- 基础直播模拟功能
- 电商商品分析
- 语音识别集成
- AI总结报告
- 点赞飘心效果

## 📞 技术支持

如有问题或建议，请联系开发团队。

## 📄 许可证

本项目仅供学习和练习使用。
