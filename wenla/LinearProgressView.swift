//
//  LinearProgressView.swift
//  wenla
//
//  Created by AI Assistant on 2025-07-12.
//

import SwiftUI

struct LinearProgressView: View {
    let progress: Double
    let description: String
    let color: Color
    
    init(progress: Double, description: String, color: Color = .orange) {
        self.progress = progress
        self.description = description
        self.color = color
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 进度百分比和鼓励信息
            VStack(spacing: 8) {
                Text("\(Int(progress * 100))%")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(color)
                    .animation(.easeInOut(duration: 0.3), value: progress)

                // 添加鼓励性的副标题
                Text(getEncouragingMessage())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .animation(.easeInOut(duration: 0.5), value: progress)
            }
            
            // 直线进度条
            VStack(spacing: 8) {
                // 进度条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        Rectangle()
                            .fill(color.opacity(0.2))
                            .frame(height: 6)
                            .cornerRadius(3)
                        
                        // 进度条
                        Rectangle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [color, color.opacity(0.8)]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * progress, height: 6)
                            .cornerRadius(3)
                            .animation(.easeInOut(duration: 0.3), value: progress)
                    }
                }
                .frame(height: 6)
                
                // 描述文字
                Text(description)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(color)
                    .multilineTextAlignment(.center)
                    .animation(.easeInOut(duration: 0.3), value: description)
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: color.opacity(0.2), radius: 10, x: 0, y: 5)
        )
        .padding(.horizontal, 20)
    }

    // 根据进度返回鼓励性信息
    private func getEncouragingMessage() -> String {
        let progressPercent = Int(progress * 100)

        switch progressPercent {
        case 0...10:
            return "AI正在干活，马上就好"
        case 11...30:
            return "AI正在干活，马上就好"
        case 31...50:
            return "AI正在干活，马上就好"
        case 51...70:
            return "AI正在干活，马上就好"
        case 71...90:
            return "AI正在干活，马上就好"
        case 91...99:
            return "AI正在干活，马上就好"
        case 100:
            return "分析完成！"
        default:
            return "AI正在干活，马上就好"
        }
    }
}

// 预览
struct LinearProgressView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.gray.opacity(0.1)
                .ignoresSafeArea()
            
            LinearProgressView(
                progress: 0.65,
                description: "正在分析商品特性...",
                color: .orange
            )
        }
    }
}
