//
//  CameraPreviewView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/4.
//

import SwiftUI
import AVFoundation

struct CameraPreviewView: UIViewRepresentable {
    @ObservedObject var cameraManager: CameraManager

    func makeUIView(context: Context) -> CameraPreviewUIView {
        let view = CameraPreviewUIView()
        view.backgroundColor = UIColor.black
        view.cameraManager = cameraManager
        return view
    }

    func updateUIView(_ uiView: CameraPreviewUIView, context: Context) {
        uiView.cameraManager = cameraManager
        uiView.updatePreviewLayer()
    }
}

// 自定义UIView来更好地管理预览层
class CameraPreviewUIView: UIView {
    var cameraManager: CameraManager? {
        didSet {
            updatePreviewLayer()
        }
    }

    private var previewLayer: AVCaptureVideoPreviewLayer?

    override func layoutSubviews() {
        super.layoutSubviews()
        // 当布局改变时更新预览层frame
        previewLayer?.frame = bounds
        print("📷 预览层frame已更新：\(bounds)")
    }

    func updatePreviewLayer() {
        // 移除旧的预览层
        previewLayer?.removeFromSuperlayer()
        previewLayer = nil

        // 添加新的预览层
        guard let newPreviewLayer = cameraManager?.previewLayer else {
            print("📷 没有可用的预览层")
            return
        }

        previewLayer = newPreviewLayer
        newPreviewLayer.frame = bounds
        newPreviewLayer.videoGravity = .resizeAspectFill
        layer.insertSublayer(newPreviewLayer, at: 0)
        print("📷 预览层已添加，尺寸：\(bounds)")
    }
}
