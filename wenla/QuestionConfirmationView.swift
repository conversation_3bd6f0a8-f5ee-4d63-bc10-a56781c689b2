//
//  QuestionConfirmationView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import SwiftUI

struct QuestionConfirmationView: View {
    @Binding var isPresented: Bool
    @ObservedObject var ecommerceManager: ECommerceManager
    let questions: [CustomerQuestion]
    let onConfirm: ([CustomerQuestion]) -> Void
    
    @State private var selectedQuestions: Set<UUID> = []
    @State private var showingModelSelector = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                Color.black.opacity(0.95)
                    .ignoresSafeArea()
                
                VStack(spacing: 20) {
                    // 标题区域
                    headerSection
                    
                    // AI模型信息
                    modelInfoSection
                    
                    // 问题列表
                    questionListSection
                    
                    Spacer()
                    
                    // 底部按钮
                    bottomButtonsSection
                }
                .padding()
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            // 默认选中所有问题
            selectedQuestions = Set(questions.map { $0.id })
        }
        .sheet(isPresented: $showingModelSelector) {
            AIModelSelectorView(aiModelManager: ecommerceManager.aiModelManager)
        }
    }
    
    // MARK: - 标题区域
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("分析完成")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text("请确认问题后发送到直播弹幕")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - AI模型信息
    private var modelInfoSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("使用模型")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(ecommerceManager.aiModelManager.currentModel.displayName)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
            
            Spacer()
            
            Button(action: {
                showingModelSelector = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "slider.horizontal.3")
                    Text("切换模型")
                }
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 问题列表
    private var questionListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("生成的问题 (\(questions.count))")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    if selectedQuestions.count == questions.count {
                        selectedQuestions.removeAll()
                    } else {
                        selectedQuestions = Set(questions.map { $0.id })
                    }
                }) {
                    Text(selectedQuestions.count == questions.count ? "取消全选" : "全选")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(questions) { question in
                        questionRow(question: question)
                    }
                }
            }
            .frame(maxHeight: 300)
        }
    }
    
    // MARK: - 问题行
    private func questionRow(question: CustomerQuestion) -> some View {
        let isSelected = selectedQuestions.contains(question.id)
        
        return HStack(alignment: .top, spacing: 12) {
            // 选择按钮
            Button(action: {
                if isSelected {
                    selectedQuestions.remove(question.id)
                } else {
                    selectedQuestions.insert(question.id)
                }
            }) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .blue : .gray)
            }
            
            // 问题内容
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(question.category)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.orange.opacity(0.2))
                        .cornerRadius(4)

                    Spacer()
                }
                
                Text(question.question)
                    .font(.body)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color.gray.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color.blue.opacity(0.3) : Color.clear, lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
    
    // MARK: - 底部按钮
    private var bottomButtonsSection: some View {
        VStack(spacing: 12) {
            // 选中数量提示
            if !selectedQuestions.isEmpty {
                Text("已选择 \(selectedQuestions.count) 个问题")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            HStack(spacing: 16) {
                // 取消按钮
                Button(action: {
                    isPresented = false
                }) {
                    Text("取消")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(12)
                }
                
                // 确认按钮
                Button(action: {
                    let confirmedQuestions = questions.filter { selectedQuestions.contains($0.id) }
                    onConfirm(confirmedQuestions)
                    isPresented = false
                }) {
                    Text("确认发送 (\(selectedQuestions.count))")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            selectedQuestions.isEmpty ? Color.gray.opacity(0.3) : Color.blue
                        )
                        .cornerRadius(12)
                }
                .disabled(selectedQuestions.isEmpty)
            }
        }
    }
}

// MARK: - AI模型选择器
struct AIModelSelectorView: View {
    @ObservedObject var aiModelManager: AIModelManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("选择AI模型")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)
                
                Text("不同模型有不同的特点和响应速度")
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                LazyVStack(spacing: 12) {
                    ForEach(AIModel.allCases, id: \.self) { model in
                        modelSelectionRow(model: model)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func modelSelectionRow(model: AIModel) -> some View {
        let isSelected = aiModelManager.currentModel == model
        
        return Button(action: {
            aiModelManager.switchToModel(model)
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(model.displayName)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(model.provider.rawValue.uppercased())
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if model.isDefault {
                    Text("推荐")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.orange.opacity(0.2))
                        .cornerRadius(4)
                }
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color.gray.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue.opacity(0.3) : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    QuestionConfirmationView(
        isPresented: .constant(true),
        ecommerceManager: ECommerceManager(),
        questions: [
            CustomerQuestion(question: "这个产品的质量怎么样？", category: "质量耐用", priority: 1),
            CustomerQuestion(question: "价格能便宜点吗？", category: "性价比", priority: 2)
        ],
        onConfirm: { _ in }
    )
}
