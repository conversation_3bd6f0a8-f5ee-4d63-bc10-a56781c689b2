//
//  ProductsAndPrompts.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/11.
//

import Foundation
import SwiftUI

// MARK: - 商品数据管理
struct ProductDataManager {
    
    // MARK: - 快捷商品数据
    static let quickProducts: [QuickProduct] = [
        QuickProduct(
            name: "Phala稳稳垫",
            description: "自带瑜伽课程的瑜伽垫",
            icon: "figure.yoga",
            iconColor: .purple,
            productInfo: ProductInfo(
                name: "Phala稳稳垫 智能瑜伽垫",
                description: "Phala稳稳垫是一款革命性的智能瑜伽垫，内置专业瑜伽课程指导系统。采用天然橡胶材质，提供卓越的防滑性能和舒适体验。配备智能感应技术，能够实时监测您的瑜伽姿势，提供个性化的课程建议和纠正指导。",
                price: "¥399",
                imageURL: "https://example.com/yoga-mat.jpg",
                features: [
                    "内置50+专业瑜伽课程",
                    "智能姿势识别与纠正",
                    "天然橡胶材质，环保防滑",
                    "6mm厚度，完美缓冲保护",
                    "配套APP，个性化训练计划",
                    "防水易清洁，持久耐用",
                    "适合初学者到高级练习者"
                ],
                category: "运动健身"
            )
        ),
        
        QuickProduct(
            name: "智能蓝牙耳机",
            description: "降噪音质双重体验",
            icon: "headphones",
            iconColor: .blue,
            productInfo: ProductInfo(
                name: "AirPods Pro Max 智能降噪耳机",
                description: "采用最新的主动降噪技术，提供沉浸式音频体验。高保真音质，舒适佩戴，长续航设计，是音乐爱好者和专业人士的理想选择。",
                price: "¥2999",
                imageURL: "https://example.com/airpods.jpg",
                features: [
                    "主动降噪技术，阻隔外界噪音",
                    "Hi-Fi高保真音质",
                    "30小时超长续航",
                    "快充技术，15分钟充电3小时使用",
                    "智能触控操作",
                    "IPX4防水等级",
                    "多设备无缝切换"
                ],
                category: "数码电子"
            )
        ),
        
        QuickProduct(
            name: "护肤精华套装",
            description: "7天见效美白淡斑",
            icon: "drop.fill",
            iconColor: .pink,
            productInfo: ProductInfo(
                name: "雅诗兰黛小棕瓶精华套装",
                description: "经典小棕瓶精华，蕴含多种活性成分，深层滋养肌肤，改善肌肤质地，提亮肤色，减少细纹，让肌肤重现年轻光彩。",
                price: "¥1299",
                imageURL: "https://example.com/skincare.jpg",
                features: [
                    "7天见效，肌肤明显改善",
                    "多重活性成分，深层修护",
                    "提亮肤色，淡化色斑",
                    "减少细纹，紧致肌肤",
                    "温和配方，敏感肌可用",
                    "小分子技术，快速吸收",
                    "套装包含精华+面霜+眼霜"
                ],
                category: "美妆护肤"
            )
        ),

        QuickProduct(
            name: "智能手表",
            description: "健康监测运动伴侣",
            icon: "applewatch",
            iconColor: .green,
            productInfo: ProductInfo(
                name: "Apple Watch Series 9 智能手表",
                description: "全新S9芯片，更快更智能。全天候健康监测，精准运动追踪，长续航设计。支持血氧检测、心率监测、睡眠分析等多项健康功能。",
                price: "¥2999",
                imageURL: "https://example.com/watch.jpg",
                features: [
                    "S9芯片，性能提升20%",
                    "全天候健康监测",
                    "100+运动模式",
                    "18小时长续航",
                    "防水50米",
                    "ECG心电图功能",
                    "血氧饱和度检测"
                ],
                category: "数码电子"
            )
        ),

        QuickProduct(
            name: "咖啡机",
            description: "一键制作专业咖啡",
            icon: "cup.and.saucer.fill",
            iconColor: .brown,
            productInfo: ProductInfo(
                name: "德龙全自动咖啡机",
                description: "意大利进口，专业级咖啡制作体验。一键操作，多种咖啡选择，内置研磨系统，新鲜现磨现煮，让您在家享受咖啡厅级别的美味咖啡。",
                price: "¥3999",
                imageURL: "https://example.com/coffee.jpg",
                features: [
                    "意大利进口品质",
                    "一键制作多种咖啡",
                    "内置陶瓷研磨器",
                    "15bar高压萃取",
                    "自动清洁功能",
                    "可调节浓度和温度",
                    "1.8L大容量水箱"
                ],
                category: "家居电器"
            )
        ),

        QuickProduct(
            name: "无线充电器",
            description: "15W快充支持多设备",
            icon: "battery.100.bolt",
            iconColor: .yellow,
            productInfo: ProductInfo(
                name: "Anker 15W无线快充充电器",
                description: "支持iPhone、Android等多种设备，15W快速充电，智能温控保护，轻薄便携设计，办公桌面的完美搭配。",
                price: "¥199",
                imageURL: "https://example.com/charger.jpg",
                features: [
                    "15W快速无线充电",
                    "支持多种设备",
                    "智能温控保护",
                    "轻薄便携设计",
                    "LED指示灯",
                    "防滑底座",
                    "过充保护"
                ],
                category: "数码配件"
            )
        )
    ]
    
    // MARK: - 根据类别获取商品
    static func getProductsByCategory(_ category: String) -> [QuickProduct] {
        return quickProducts.filter { $0.productInfo.category == category }
    }
    
    // MARK: - 获取所有商品类别
    static func getAllCategories() -> [String] {
        return Array(Set(quickProducts.map { $0.productInfo.category }))
    }
    
    // MARK: - 根据ID获取商品
    static func getProduct(by id: UUID) -> QuickProduct? {
        return quickProducts.first { $0.id == id }
    }
}

// MARK: - AI提示词管理
struct AIPromptManager {
    
    // MARK: - 商品分析提示词（分批生成）
    static func getProductAnalysisPrompt(for product: ProductInfo, batch: Int, category: String) -> String {
        return """
        请为以下商品生成5个关于\(category)的客户问题：

        商品：\(product.name)
        价格：\(product.price)
        特点：\(product.features.joined(separator: "、"))

        专注于\(category)方面，生成5个客户最关心的问题。

        要求：
        - 问题要实用，像真实用户会问的
        - 表达自然，便于直播回答
        - 每行一个问题，不需要编号
        - 专注于\(category)相关内容

        示例格式：
        这个产品质量怎么样？
        使用起来方便吗？
        性价比如何？
        """
    }

    // MARK: - 获取分批类别
    static func getBatchCategories() -> [String] {
        return [
            "产品功能和特性",
            "质量和耐用性",
            "使用体验和效果",
            "性价比分析",
            "售后服务保障",
            "产品对比优势",
            "适用场景人群",
            "购买决策建议",
            "用户评价反馈",
            "常见问题解答"
        ]
    }
    
    // MARK: - 直播表现分析提示词
    static func getLiveStreamAnalysisPrompt(
        productName: String,
        duration: TimeInterval,
        questionCount: Int,
        topQuestionCategories: String,
        transcriptSample: String
    ) -> String {
        return """
        请分析以下直播带货表现：

        基本信息：
        - 直播时长：\(Int(duration/60))分钟
        - 产品：\(productName)
        - 客户问题数：\(questionCount)

        请给出：
        1. 整体评分（0-100分）
        2. 3条优化建议
        3. 表现总结（30字内）

        格式：
        评分：XX分
        建议：
        1. xxx
        2. xxx
        3. xxx
        总结：xxx
        """
    }
    
    // MARK: - 测试AI连接提示词
    static let testConnectionPrompt = "测试AI模型连接，请简单回复'连接成功'"
    
    // MARK: - 备用问题生成提示词
    static func getFallbackQuestionsPrompt(for productCategory: String) -> [String] {
        let baseQuestions = [
            "这个产品的质量怎么样？",
            "使用起来方便吗？",
            "性价比如何？",
            "有什么优缺点？",
            "适合什么人群使用？",
            "售后服务好吗？",
            "与同类产品相比有什么优势？",
            "使用寿命大概多长？",
            "有没有什么使用注意事项？",
            "值得购买吗？"
        ]
        
        // 根据商品类别添加特定问题
        var categorySpecificQuestions: [String] = []
        
        switch productCategory {
        case "运动健身":
            categorySpecificQuestions = [
                "运动效果明显吗？",
                "适合初学者使用吗？",
                "会不会容易受伤？",
                "需要专业指导吗？"
            ]
        case "数码电子":
            categorySpecificQuestions = [
                "电池续航怎么样？",
                "兼容性如何？",
                "会不会很快过时？",
                "技术参数达标吗？"
            ]
        case "美妆护肤":
            categorySpecificQuestions = [
                "敏感肌可以用吗？",
                "多久能看到效果？",
                "会不会有副作用？",
                "适合什么年龄段？"
            ]
        default:
            categorySpecificQuestions = [
                "品牌可靠吗？",
                "有优惠活动吗？",
                "发货速度快吗？",
                "包装怎么样？"
            ]
        }
        
        return baseQuestions + categorySpecificQuestions
    }
}
