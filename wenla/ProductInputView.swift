//
//  ProductInputView.swift
//  wenla
//
//  Created by <PERSON> on 2025/7/7.
//

import SwiftUI

struct ProductInputView: View {
    @Binding var isPresented: Bool
    @ObservedObject var ecommerceManager: ECommerceManager
    @State private var selectedModel: AIModel = .doubaoSeed
    @State private var showingModelPicker = false
    @State private var selectedProduct: QuickProduct? = nil
    
    var body: some View {
        ZStack {
            // 主背景 - 改为白色背景
            Color.white
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // 标题区域
                VStack(spacing: 16) {
                    // 图标和标题
                    HStack(spacing: 12) {
                        Image(systemName: "cart.fill")
                            .font(.system(size: 32))
                            .foregroundColor(.orange)
                            .shadow(color: .orange.opacity(0.3), radius: 4, x: 0, y: 2)

                        Text("选择商品")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                    }

                    Text("选择快捷商品开始直播带货训练")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)

                // 快捷商品选择区域
                quickProductSelectionSection

                Spacer()

                // 底部提示文本
                VStack(spacing: 16) {
                    Text("点击商品卡片开始直播带货训练")
                        .font(.body)
                        .foregroundColor(.gray)
                        .padding(.vertical, 20)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
            }
        }
        .sheet(isPresented: $ecommerceManager.showQuestionConfirmation) {
            QuestionConfirmationView(
                isPresented: $ecommerceManager.showQuestionConfirmation,
                ecommerceManager: ecommerceManager,
                questions: ecommerceManager.pendingQuestions,
                onConfirm: { confirmedQuestions in
                    ecommerceManager.confirmQuestions(confirmedQuestions)
                    isPresented = false
                }
            )
        }
        .onAppear {
            selectedModel = ecommerceManager.aiModelManager.currentModel
        }
        .onDisappear {
            // 页面消失时不需要清理，因为分析是在后台进行的
        }
    }
    
    // MARK: - 快捷商品选择区域
    private var quickProductSelectionSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Text("热门商品")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.black)

                Spacer()

                Text("\(ProductDataManager.quickProducts.count) 个商品")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.1))
                    )
            }

            // 使用网格布局显示商品
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12)
            ], spacing: 16) {
                ForEach(ProductDataManager.quickProducts, id: \.id) { product in
                    quickProductCard(product)
                }
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - 快捷商品卡片
    private func quickProductCard(_ product: QuickProduct) -> some View {
        Button(action: {
            // 选择商品后直接发送
            selectedProduct = product
            
            // 设置选中的AI模型
            ecommerceManager.aiModelManager.switchToModel(selectedModel)
            
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            // 立即关闭页面
            isPresented = false
            
            // 在后台开始分析
            Task {
                await ecommerceManager.analyzeQuickProductInBackground(product.productInfo)
            }
        }) {
            VStack(spacing: 8) {
                // 商品图标 - 简化
                Image(systemName: product.icon)
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(product.iconColor)
                    .frame(width: 50, height: 50)

                // 商品信息 - 简化
                VStack(spacing: 4) {
                    Text(product.productInfo.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)

                    Text(product.productInfo.price)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(product.iconColor)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
                    .shadow(
                        color: .gray.opacity(0.1),
                        radius: 4,
                        x: 0,
                        y: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProductInputView(
        isPresented: .constant(true),
        ecommerceManager: ECommerceManager()
    )
}
