// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		1431C28D2E180D7C00271F6E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1431C2772E180D7A00271F6E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1431C27E2E180D7A00271F6E;
			remoteInfo = wenla;
		};
		1431C2972E180D7C00271F6E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1431C2772E180D7A00271F6E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1431C27E2E180D7A00271F6E;
			remoteInfo = wenla;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1431C27F2E180D7A00271F6E /* wenla.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = wenla.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1431C28C2E180D7C00271F6E /* wenlaTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wenlaTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1431C2962E180D7C00271F6E /* wenlaUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wenlaUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1431C2812E180D7A00271F6E /* wenla */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wenla;
			sourceTree = "<group>";
		};
		1431C28F2E180D7C00271F6E /* wenlaTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wenlaTests;
			sourceTree = "<group>";
		};
		1431C2992E180D7C00271F6E /* wenlaUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wenlaUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1431C27C2E180D7A00271F6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C2892E180D7C00271F6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C2932E180D7C00271F6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1431C2762E180D7A00271F6E = {
			isa = PBXGroup;
			children = (
				1431C2812E180D7A00271F6E /* wenla */,
				1431C28F2E180D7C00271F6E /* wenlaTests */,
				1431C2992E180D7C00271F6E /* wenlaUITests */,
				1431C2802E180D7A00271F6E /* Products */,
			);
			sourceTree = "<group>";
		};
		1431C2802E180D7A00271F6E /* Products */ = {
			isa = PBXGroup;
			children = (
				1431C27F2E180D7A00271F6E /* wenla.app */,
				1431C28C2E180D7C00271F6E /* wenlaTests.xctest */,
				1431C2962E180D7C00271F6E /* wenlaUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1431C27E2E180D7A00271F6E /* wenla */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1431C2A02E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenla" */;
			buildPhases = (
				1431C27B2E180D7A00271F6E /* Sources */,
				1431C27C2E180D7A00271F6E /* Frameworks */,
				1431C27D2E180D7A00271F6E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1431C2812E180D7A00271F6E /* wenla */,
			);
			name = wenla;
			packageProductDependencies = (
			);
			productName = wenla;
			productReference = 1431C27F2E180D7A00271F6E /* wenla.app */;
			productType = "com.apple.product-type.application";
		};
		1431C28B2E180D7C00271F6E /* wenlaTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1431C2A32E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenlaTests" */;
			buildPhases = (
				1431C2882E180D7C00271F6E /* Sources */,
				1431C2892E180D7C00271F6E /* Frameworks */,
				1431C28A2E180D7C00271F6E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1431C28E2E180D7C00271F6E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1431C28F2E180D7C00271F6E /* wenlaTests */,
			);
			name = wenlaTests;
			packageProductDependencies = (
			);
			productName = wenlaTests;
			productReference = 1431C28C2E180D7C00271F6E /* wenlaTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1431C2952E180D7C00271F6E /* wenlaUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1431C2A62E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenlaUITests" */;
			buildPhases = (
				1431C2922E180D7C00271F6E /* Sources */,
				1431C2932E180D7C00271F6E /* Frameworks */,
				1431C2942E180D7C00271F6E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1431C2982E180D7C00271F6E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1431C2992E180D7C00271F6E /* wenlaUITests */,
			);
			name = wenlaUITests;
			packageProductDependencies = (
			);
			productName = wenlaUITests;
			productReference = 1431C2962E180D7C00271F6E /* wenlaUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1431C2772E180D7A00271F6E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					1431C27E2E180D7A00271F6E = {
						CreatedOnToolsVersion = 16.4;
					};
					1431C28B2E180D7C00271F6E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 1431C27E2E180D7A00271F6E;
					};
					1431C2952E180D7C00271F6E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 1431C27E2E180D7A00271F6E;
					};
				};
			};
			buildConfigurationList = 1431C27A2E180D7A00271F6E /* Build configuration list for PBXProject "wenla" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1431C2762E180D7A00271F6E;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1431C2802E180D7A00271F6E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1431C27E2E180D7A00271F6E /* wenla */,
				1431C28B2E180D7C00271F6E /* wenlaTests */,
				1431C2952E180D7C00271F6E /* wenlaUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1431C27D2E180D7A00271F6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C28A2E180D7C00271F6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C2942E180D7C00271F6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1431C27B2E180D7A00271F6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C2882E180D7C00271F6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1431C2922E180D7C00271F6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1431C28E2E180D7C00271F6E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1431C27E2E180D7A00271F6E /* wenla */;
			targetProxy = 1431C28D2E180D7C00271F6E /* PBXContainerItemProxy */;
		};
		1431C2982E180D7C00271F6E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1431C27E2E180D7A00271F6E /* wenla */;
			targetProxy = 1431C2972E180D7C00271F6E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1431C29E2E180D7C00271F6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1431C29F2E180D7C00271F6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1431C2A12E180D7C00271F6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9J22D339DU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app uses the camera to provide live streaming practice experience. The camera feed helps you practice being a streamer and interact with simulated audience.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app uses the microphone to record your voice during live streaming practice, helping you improve your presentation skills.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "This app uses speech recognition to analyze your live streaming content and provide feedback to help you improve your streaming performance.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenla;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1431C2A22E180D7C00271F6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9J22D339DU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app uses the camera to provide live streaming practice experience. The camera feed helps you practice being a streamer and interact with simulated audience.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app uses the microphone to record your voice during live streaming practice, helping you improve your presentation skills.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "This app uses speech recognition to analyze your live streaming content and provide feedback to help you improve your streaming performance.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenla;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1431C2A42E180D7C00271F6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenlaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wenla.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wenla";
			};
			name = Debug;
		};
		1431C2A52E180D7C00271F6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenlaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wenla.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wenla";
			};
			name = Release;
		};
		1431C2A72E180D7C00271F6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenlaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = wenla;
			};
			name = Debug;
		};
		1431C2A82E180D7C00271F6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = pubu.wenlaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = wenla;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1431C27A2E180D7A00271F6E /* Build configuration list for PBXProject "wenla" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1431C29E2E180D7C00271F6E /* Debug */,
				1431C29F2E180D7C00271F6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1431C2A02E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenla" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1431C2A12E180D7C00271F6E /* Debug */,
				1431C2A22E180D7C00271F6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1431C2A32E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenlaTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1431C2A42E180D7C00271F6E /* Debug */,
				1431C2A52E180D7C00271F6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1431C2A62E180D7C00271F6E /* Build configuration list for PBXNativeTarget "wenlaUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1431C2A72E180D7C00271F6E /* Debug */,
				1431C2A82E180D7C00271F6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1431C2772E180D7A00271F6E /* Project object */;
}
